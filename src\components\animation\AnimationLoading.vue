<template>
    <view class="spinner" ref="spinner">
        <view class="spinner1"></view>
        <view class="spinner2" v-if="timeLeft <= 60"></view>
        <view class="spinner-text">
            <template v-if="timeLeft > 0">
                <view class="line-1"><text :class="{breathe: timeLeft > 60}">{{ timeLeft }}</text><text class="text-unit">s</text></view>
                <view class="line-2">{{ props.subTitle }}</view>
            </template>
            <view class="line-1 hourglass" v-else>⏳</view>
        </view>
    </view>
</template>
<script setup>
import { ref,watch, defineProps } from 'vue';


const props = defineProps({
    nextMeasureTime: {
        type: String,
        default: '',
    },
    subTitle: {
        type: String,
        default: '等待着'
    }
})


// 剩余时间初始化为0
const timeLeft = ref(0);

// 计算距离目标时间的剩余秒数
const calculateTimeLeft = () => {
    const now = new Date().getTime();
    const targetTime = new Date(props.nextMeasureTime).getTime();
    const difference = targetTime - now;
    return Math.max(Math.floor(difference / 1000), 0); // 确保不出现负数
};

// 更新倒计时
const updateCountdown = () => {
    timeLeft.value = calculateTimeLeft();

    // 如果时间还没到目标时间，继续每秒更新
    if (timeLeft.value > 0) {
        setTimeout(updateCountdown, 1000);
    }
};

watch(() => props.nextMeasureTime, (val, old) => {
    if (val !== old) {
        updateCountdown();
    }
}, { immediate: true })
</script>


<style lang="scss" scoped>
.spinner {
    position: relative; 
    display: flex;
    flex-shrink: 0;
    flex-grow: 0;
    flex-basis: auto;
    z-index: 1;
    width: 100%;
    height: 100%;
    .spinner1 {
        position: absolute;
        width: 100%;
        height: 100%;
        top: 1rpx;
        left: 0;
        z-index: 1;
        border-radius: 50%;
        border: 16rpx solid;
        border-color: rgba(21, 156, 173, 1);
        box-sizing: border-box;
    }

    .spinner2 {
        position: absolute;
        top: 1rpx;
        left: 0;
        z-index: 2;
        width: 100%;
        height: 100%;
        border-radius: 50%;
        background:
            radial-gradient(farthest-side, rgba(51, 223, 238, 1) 100%, #0000) top/16rpx 16rpx no-repeat,
            conic-gradient(#0000 20%, rgba(51, 223, 238, 1));
        -webkit-mask: radial-gradient(farthest-side, #0000 calc(100% - 16rpx), #000 0);
        animation: s3 0.75s infinite linear;
    }

    .spinner-text {

        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        text-align: center;
        justify-content: center;
        .breathe {
            display: inline-block;
            animation: breathe 2s infinite;
            // animation-timing-function: ease-in-out;
        }
        .line-1 {

            color: #33DFEE;
            font-size: 50rpx;
            .text-unit {
                font-size: 26rpx;
                padding-left: 4rpx;
            }
        }
        .line-2 {
            position: relative;
            top: -4rpx;
            font-size: 24rpx;
        }
        .hourglass {
            content: '⏳';
            animation: rotate-hourglass 6s infinite;
        }
    }
}

@keyframes s3 {
    100% {
        transform: rotate(1turn)
    }
}

@keyframes rotate-hourglass {
  0% {
    transform: rotate(0deg); /* 初始状态 */
  }
  16.67% {
    transform: rotate(-180deg); /* 向左旋转90度，持续1秒 */
  }
  50% {
    transform: rotate(-180deg); /* 停顿2秒，保持左旋转 */
  }
  66.67% {
    transform: rotate(0deg); /* 向右旋转90度，持续1秒 */
  }
  100% {
    transform: rotate(0deg); /* 停顿2秒，保持右旋转 */
  }
}
/* 定义呼吸动画 */
@keyframes breathe {
    0% {
        transform: scale(1); /* 初始状态 */
    }
    50% {
        transform: scale(1.1); /* 中间状态，变大到1.2倍 */
    }
    100% {
        transform: scale(1); /* 结束状态，回到原始大小 */
    }
}
</style>