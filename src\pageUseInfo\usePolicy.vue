<template>
    <view class="bg-[#f7f9fc]">
        <!-- <view class="bg-white p-20rpx text-center font-bold">{{ title }}</view> -->
        <text class="inline-block m-24rpx p-24rpx bg-white indent-36rpx rounded-16rpx">{{ content }}</text>
    </view>
</template>
<script setup lang="ts">
import { ref } from "vue";

import { onLoad, onShow } from "@dcloudio/uni-app";

import protocolData from "./protocol";

const title = ref("隐私条款");

const content = ref('');

onLoad((option: any) => {
    uni.showLoading({
        mask: true,
    });
});

onShow(() => {
    setTimeout(() => {
        uni.hideLoading();
        content.value = protocolData.content;
    }, 1000)
});
</script>
<style lang="scss" scoped>
// .container {
//     .content {
//         display: inline-block;
//         padding: 24rpx;
//         margin: 24rpx;
//         text-indent: 2rem;
//         background: white;
//         border-radius: 16rpx;
//     }
// }
</style>