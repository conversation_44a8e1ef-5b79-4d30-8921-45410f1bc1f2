<template>
	<view class="content">
		<image :src="baseUrl + '/need/static/img/my/about.png'" mode="widthFix" ></image>
	</view>
</template>

<script>
import environment from '@/common/environments' // 环境，服务配置文件
export default {
	data() {
		return {
			baseUrl: ''
		};
	},
	computed: {
	},
	methods: {
	},
	onLoad(option) {
        this.baseUrl = environment.envConfigs.url
	},
	onShow() {
	},
	onUnload() {
	}
};
</script>

<style lang="scss" scoped>
image {
	width: 100%;
	// height: 970px;
}
</style>
