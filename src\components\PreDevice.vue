<template>
    <view>
        <view class="m-[30rpx] flex justify-center">
		    <u-tabs :list="products" :activeStyle="{
                color: '#014777',
                fontSize:'46rpx',
                transform: 'scale(1.05)'
            }" :inactiveStyle="{
                color: '#333',
                fontSize:'36rpx',
                transform: 'scale(1)'
            }" @click="click"></u-tabs>
        </view>
        <view>
            <view v-for="(item, index) in filterData" :key="index" class="flex flex-row items-center bg-white rounded-[16rpx] mt-[30rpx] py-[30rpx] px-[40rpx]">
                <image :src="item.image" class="w-[140rpx] h-[140rpx] object-contain" mode="widthFix"/>
                <view class="flex flex-col pl-[20rpx] w-full">
                    <text>{{ item.title }}</text>
                    <view class="flex flex-row justify-between text-sm pt-[40rpx]">
                        <view class="flex items-center" @click="onClickVideo(item.title)"><uni-icons custom-prefix="iconfont" type="icon-shipin"
                                size="23"></uni-icons>&nbsp;使用教程</view>
                        <view class="flex items-center" @click="onClickAddDevice"><uni-icons custom-prefix="iconfont" type="icon-icon-"
                                size="23"></uni-icons>&nbsp;绑定设备</view>
                    </view>
                </view>
            </view>
        </view>

        <view v-if="videoInfo.videoplay" style="position: absolute; top: -999px;">
            <video
            autoplay
            id="myvideo"
            :src="videoInfo.videoUrl"
            @fullscreenchange="screenChange"
            ></video>
        </view>
    </view>
</template>
<script setup lang="ts">
    import environment from '@/common/environments' // 环境，服务配置文件
	import { reactive, ref, computed, getCurrentInstance } from "vue";

    const instance = getCurrentInstance();

	const products = reactive([
		{ name: '血压' },
		{ name: '体温' },
		{ name: '血氧' }
	]);
    // 当前选中的 Tab
	const selectedTab = ref('血压');

    import device0 from '/static/products/thermometer01.png';
    import device1 from '/static/products/thermometer02.png';
    import device2 from '/static/products/blood-pressure02.png';
    import device3 from '/static/products/blood-pressure01.png';
    import device4 from '/static/products/bool-oxygen01.png';
    import device5 from '/static/products/bool-oxygen02.png';
    const imageDeviceList = {
        BCB : "充电盒",
        DRIP : "点滴监护仪",

        HYO2 : device3,
        SPHY : device2,

        THERMOMETER : device0,
        THERMOMETERSPLINCHED : device1,
        
        SPO2 : device4,
        RSPO2: device5
    }
    const dataList = ref([
        {
            name: '血压',
            title: '血压血氧一体机',
            videoName: 'HYO2',
            image: imageDeviceList.HYO2,
        },
        {
            name: '血压',
            title: '远程动态血压监测系统',
            videoName: 'SPHY',
            image: imageDeviceList.SPHY,
        },

        {
            name: '体温',
            title: '动态无线电子体温计',
            videoName: 'THERMOMETER',
            image: imageDeviceList.THERMOMETER,
        },
        {
            name: '体温',
            title: '连续测量电子体温计',
            videoName: 'THERMOMETERSPLINCHED',
            image: imageDeviceList.THERMOMETERSPLINCHED,
        },

        {
            name: '血氧',
            title: '无线动态医用脉搏血氧仪',
            videoName: 'SPO2',
            image: imageDeviceList.SPO2,
        },
        {
            name: '血氧',
            title: '医用脉搏血氧仪',
            videoName: 'RSPO2',
            image: imageDeviceList.RSPO2,
        },
    ])

    const filterData = computed(() => {
        return dataList.value.filter(item => item.name === selectedTab.value);
    })

    // 定义方法  
	function click(item : any) {
		selectedTab.value = item.name;
		console.log('选中的 Tab:', item.name);
	}

    function onClickAddDevice() {
        uni.navigateTo({
            url: '/pageDevice/addDevice',
            animationType: 'slide-in-bottom'
        });
    }

    const videoInfo = reactive({
        baseUrl: environment.envConfigs.url + '/need/static/video/',
        videoplay: false,
        videoUrl: '',
    })
    function onClickVideo(title: string) {
        videoInfo.videoplay = true;
        const videoContext = uni.createVideoContext("myvideo", instance); 
        setTimeout(() => {
            videoInfo.videoUrl = `${videoInfo.baseUrl}${title}.mp4`;
            videoContext.requestFullScreen({ direction: 0 });  //direction: 90  控制全屏的时候视屏旋转多少度
            videoContext.play();
        }, 200);
    }
    function screenChange(e: any) {
        let fullScreen = e.detail.fullScreen; //值true为进入全屏，false为退出全屏
        if (!fullScreen) {
            //退出全屏
            videoInfo.videoplay = false;      // 隐藏播放盒子
        }
    }
</script>