<template>
    <view class="bg-white rounded-[16rpx] py-[40rpx] flex flex-col">
        <!-- <view class="text-[28rpx]">
            <text class="text-[36rpx] font-bold pr-[20rpx]">{{ props.patient.patientName }}</text>
            <text class="pr-[16rpx]">{{ props.patient.sexText }}</text>
            <text class="pr-[16rpx]">{{ props.patient.age }}</text>
            <text class="pr-[16rpx]">预约时间：{{ moment(props.patient.appointmentTime).format('HH:mm') || '-' }}</text>
            <text>{{ props.patient.medicalHistory }}</text>
        </view> -->
        <view class="pt-[14rpx]">
            <up-steps
                :current="current" activeColor="#0FC060">
                <up-steps-item title="诊室测量"></up-steps-item>
                <up-steps-item title="设置计划"></up-steps-item>
                <up-steps-item title="院外测量"></up-steps-item>
                <up-steps-item title="诊室测量"></up-steps-item>
                <up-steps-item title="完成测量"></up-steps-item>
            </up-steps>
        </view>
    </view>
</template>
<script setup lang="ts">
	import { computed } from "vue";
    import moment from 'moment';

    const props = defineProps({
        patient: {
            type: Object,
            default: () => {},
        },
        steps: {
            type: Number,
            default: 0
        }
    })

    const current = computed(() => {
        const arr = [[10, 20], [30], [40], [50], [60]]
        return arr.findIndex(subArr => subArr.includes(props.steps))
    })


</script>