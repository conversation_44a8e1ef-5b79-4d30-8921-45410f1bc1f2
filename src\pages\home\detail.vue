<template>
    <view class="h-full flex flex-col box-border bg-[#e3ecf2]">
        <up-navbar
            :autoBack="true"
            :placeholder="true"
            bgColor="#fff"
            height="82rpx"
            >
            <template #center>
                <view class="text-[30rpx] flex">
                    <text class="pr-[16rpx] font-bold">{{ patient.patientName }}</text>
                    <text class="pr-[16rpx]">{{ patient.sexText }}</text>
                    <text class="pr-[16rpx]">{{ patient.age }}</text>
                    <text class="pr-[16rpx]">预约:{{ moment(patient.appointmentTime).format('HH:mm') || '-' }}</text>
                    <!-- <text class="inline-block overflow-hidden text-ellipsis whitespace-nowrap max-w-[160rpx]">{{ patient.medicalHistory || '' }}</text> -->
                </view>
            </template>
            <template #right>
                <up-icon 
                    name="setting" 
                    size="20" 
                    color="#666"
                    @click="showUnbindModal = true"
                    class="pr-[20rpx]"
                ></up-icon>
            </template>
        </up-navbar>
        <view class="p-[30rpx] pb-[10rpx]">
            <deviceScreen v-if="isLoadDevice" :device="device" @startMeasure="startMeasure" @refreshList="refreshData"></deviceScreen>
            <view v-else class="bg-white rounded-[25rpx] p-[30rpx] text-center color-[#666]">无设备信息</view>
        </view>
        <view class="flex-1 overflow-hidden px-[30rpx]">
            <scroll-view 
                scroll-y 
                class="h-full" 
                :scroll-into-view="scrollIntoView"
                :scroll-with-animation="true"
            >
                <view class="pb-[15rpx] pt-[35rpx]">
                  <stepBar :patient="patient" :steps="steps"></stepBar>
                </view>
                <!-- 0 诊室测量中 -->
                <view :id="'step10'">
                    <steps1 v-if="steps >= 10" ref="refSteps1" :patient="patient" :steps="steps" :device="device" @changeArm="changeArm"></steps1>
                </view>
                <view :id="'step20'">
                    <steps1Position v-if="patient.positionDiffMeasure && steps >= 20" :patient="patient" :steps="steps" :device="device" ref="refSteps2"></steps1Position>
                </view>
                <view :id="'step30'">
                    <steps2 v-if="steps >= 30 && steps !== 60" :patient="patient" :steps="steps" :device="device"></steps2>
                </view>
                <view :id="'step40'">
                    <steps3 v-if="steps >= 40" ref="refSteps3" :patient="patient" :steps="steps"></steps3>
                </view>
                <view :id="'step50'">
                    <steps4 v-if="steps >= 50" ref="refSteps4" :patient="patient" :steps="steps" :device="device"></steps4>
                </view>
                <view class="h-[120rpx]"></view>
            </scroll-view>
        </view>

        
        <view class="h-[0rpx]">
            <!-- NFC绑定弹窗 -->
            <up-popup 
            :show="showNFCPopup" 
            mode="bottom" 
            round="20"
            :closeable="true"
            @close="closeNFCPopup"
            :safeAreaInsetBottom="true"
            >   
            <template v-if="nfcAvailable">
                <view class="nfc-animation-box">
                    <image 
                        src="@/static/products/blood-pressure01.png" 
                        class="nfc-device" 
                        mode="aspectFit" 
                    />
                    <image
                        src="@/static/products/iPhone.png"
                        class="nfc-phone"
                        mode="aspectFit"
                    />
                </view>
                <view class="text-lg color-gray-600 mb-4 text-center">请将手机靠近设备NFC区域</view>
                <view class="text-sm color-gray-400 text-center">正在等待NFC识别...</view>
                <view class="h-[80rpx]"></view>
            </template>
            <template v-else>
                <view class="nfc-animation-box">
                    <image 
                        src="@/static/img/nfc.png" 
                        class="nfc-device" 
                        mode="aspectFit" 
                    />
                    <view class="nfc-retry">
                        <view class="text-sm color-gray-400 text-center">请开启手机NFC</view>
                        <view @click="onNFCRetry" class="h-[80rpx] w-[80%] leading-[80rpx] bg-[#237FC2] color-white rounded-[50rpx] text-center mt-[20rpx]">检查NFC</view>
                    </view>
                </view>

            </template>

            </up-popup>


            <!-- 解绑设备弹窗 --> 
            <up-modal
                :show="showUnbindModal"
                title="设备解绑"
                content="确定要解除当前设备绑定吗？"
                :showCancelButton="true"
                @confirm="confirmUnbind"
                @cancel="showUnbindModal = false"
                contentTextAlign="center"
            ></up-modal>
        </view>


    </view>
</template>
<script setup lang="ts">
// 显示步骤逻辑
// 1=未开始；2=诊室测量中；3=家庭测量中；4=二次诊室测量中；5=测量完成；

import deviceScreen from './components/deviceScreen.vue';
import stepBar from './components/stepBar.vue';
import { ref, reactive, onMounted, watch, computed, nextTick, onBeforeUnmount } from 'vue';
import { onLoad, onShow, onUnload } from "@dcloudio/uni-app";
import moment from 'moment';
import { getPatientBindingDevice, startBpPointMeasure, unbindDevice, bindDevice } from '@/common/api/task';
import nfc from "@/common/nfc.js";

import { useNfcInfoStore } from "@/stores/nfcInfo";

import { useTask } from '@/stores/task'
const storeUseTask = useTask();

// 步骤1
import steps1 from './components/steps1.vue'
import steps2 from './components/steps2.vue'
import steps3 from './components/steps3.vue'
import steps4 from './components/steps4.vue'
import steps1Position from './components/steps1Position.vue'

interface Patient {
    executeStatus: number;
    [key: string]: any;
}

const patient = ref<Patient>({
    executeStatus: 1
})

const device: any = ref({})
const isLoadDevice = ref(false)

/**
 *  0、未开始
 *  10、左右臂
 *  20、体位
 *  30、设置计划
 *  40、家庭测量
 *  50、二次诊室
 *  60、完成
 */
const steps = computed(() => {
    // 未绑定设备
    // if (!patient.value.deviceNo || !patient.value.executeStatus) {
    //     return 0
    // }

    // const isSaveArm = !!patient.value.armDiffMeasure
    // const positionDiffMeasure = !!patient.value.positionDiffMeasure
    

    // if (patient.value.executeStatus === 1) {
    //     return 1
    // }
    return patient.value.executeStatus
})

// 修改测量手臂
const arm = ref(1)
const changeArm = (value: number) => {
    arm.value = value
}

const startMeasure = () => {
    console.log('启动测量')


    const params = {
        arm: arm.value,
        // bpMeasureType,
        deviceNo: device.value.deviceNo,
        patientId: patient.value.patientId
    }
    startBpPointMeasure(params).then(res => {
        uni.showToast({
            title: '启动成功！',
            icon: 'success'
        })
    }).catch(error => {
        console.log(error)
        uni.showToast({
            title: '无法启动！',
            icon: 'error'
        })
    })
}

watch(() => storeUseTask.$state.isRefreshStatus, (later: boolean) => {
    if (later) {
        console.log('刷新一下')
        // 改为家庭测量 TODO 应该请求接口更新 patient
        patient.value.executeStatus = 40
        storeUseTask.$state.isRefreshStatus = false
    }
})

// 触发刷新
const refSteps1: any = ref({})
const refSteps2: any = ref({})
const refSteps3: any = ref({})
const refSteps4: any = ref({})

const stepsMapping: any = {
    10: refSteps1,
    20: refSteps2,
    40: refSteps3,
    50: refSteps4
}

const refreshData = () => {
    const currentStep = patient.value.executeStatus;
    if (stepsMapping[currentStep]) {
        const stepRef = stepsMapping[currentStep];
        stepRef.value.queryList()
    }
}

// 添加滚动控制变量
const scrollIntoView = ref('')

// 滚动到指定步骤
const scrollToStep = (step: number) => {
    setTimeout(() => {
      scrollIntoView.value = `step${step}`
    }, 300)
}
const showNFCPopup = ref(false)


onLoad((option: any) => {
    const patientData = JSON.parse(decodeURIComponent(option.patient));
    patient.value = patientData
    arm.value = patient.value.arm || 1 // 初始手臂

    if (steps.value === 60) {
        return
    }

    getPatientBindingDevice({patientId: patientData.patientId}).then((res: any) => {

        if (res.code === 420) {
            isLoadDevice.value = false
            showNFCPopup.value = true

            // #ifdef APP-PLUS
            nfc.listenNFCStatus();
            nfc.readData();
            // #endif

            return
        }

        device.value = res.data
        isLoadDevice.value = true
        // 设备加载完成后滚动到对应步骤
        if (steps.value >= 30) {
            scrollToStep(steps.value)
        }
    })
})

const onNFCRetry = (() => {
    // #ifdef APP-PLUS
    nfc.listenNFCStatus();
    nfc.readData();
    // #endif
    uni.showLoading({
        title: '加载中...',
        mask: true
    })
    setTimeout(() => {
        if (useNfcInfoStore().isNfcAvailable) {
            uni.showToast({
                icon: 'success',
                title: '启动成功!',
            })
        }else {
            uni.showToast({
                title: '检测不到NFC！',
                icon: 'error',
            });
        }

        uni.hideLoading()
    }, 1000);

})

onUnload(() => {
	// #ifdef APP-PLUS
	nfc.stopRead();
	// #endif
})

// 监听步骤变化自动滚动
watch(() => steps.value, (newStep) => {
    if (newStep >= 10) {
        scrollToStep(newStep)
    }
})

const closeNFCPopup = () => {
    // 后退
    uni.navigateBack()
}

const showUnbindModal = ref(false)
const showModel = ref(false)

// 绑定
const requestBindDevice = (deviceNo: string) => {
    // 绑定设备
    bindDevice({ patientId: patient.value.patientId, deviceNo: deviceNo }).then((res: any) => {
        if (res.success) {
            // 更新设备信息
            patient.value.deviceNo = deviceNo
            getPatientBindingDevice({patientId: patient.value.patientId}).then((res: any) => {
                device.value = res.data
                isLoadDevice.value = true
                showNFCPopup.value = false
            })
        }else if (res.code === 421) {
            const { patientId, deviceNo, patientName } = res.data
            // 要清除之前的 showModal
            if (showModel.value) {
                return
            }
            showModel.value = true
            uni.showModal({
                title: '提示',
                content: `设备已被 ${patientName} 绑定，是否强制解绑？`,
                success: (res: any) => {
                    if (res.confirm) {
                        confirmUnbind({ patientId, deviceNo })
                    }
                },
                complete: () => {
                    showModel.value = false
                }
            })
        }
    })
}
// 确认解绑
const confirmUnbind = async (obj: any) => {
    try {
        const params = {
            patientId: patient.value.patientId,
            deviceNo: device.value.deviceNo
        }
        if (obj?.patientId) {
            params.patientId = obj.patientId
        }
        if (obj?.deviceNo) {
            params.deviceNo = obj.deviceNo
        }
        await unbindDevice(params)
        
        uni.showToast({ title: '解绑成功' })

        if (obj?.patientId) {
            requestBindDevice(obj.deviceNo)
            return
        }

        showUnbindModal.value = false
        showNFCPopup.value = true // 显示NFC绑定弹窗
        isLoadDevice.value = false
    } catch (error) {
        uni.showToast({ title: '解绑失败', icon: 'none' })
    }
}

const nfcAvailable = computed(() => useNfcInfoStore().isNfcAvailable)

// 监听NFC识别
watch(
	() => useNfcInfoStore().getNfcId,
	(newValue) => {
        if (newValue === null) {
            return
        }
        requestBindDevice(newValue)
	}
);

</script>
<style scoped lang="scss">
page {
    background: #e3ecf2;
    height: 100%;
}

.nfc-animation-box {
	position: relative;
	width: 600rpx;
	height: 300rpx;
	margin: 40rpx auto;
	display: flex;
	align-items: center;
	justify-content: center;
}
.nfc-retry {
    flex: 1 1 0%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}
.nfc-device {
	width: 240rpx;
	height: 240rpx;
	flex-shrink: 0;
	position: relative;
    left: -40rpx;
	z-index: 1;
}

.nfc-phone {
	width: 200rpx;
	height: 200rpx;
	position: absolute;
	right: 0;
    top: 34px;
	animation: phone-approach 3s ease-in-out infinite;
}

@keyframes phone-approach {
	0% {
		transform: translateX(30%);
		opacity: 0;
	}
	10% {
		transform: translateX(29%);
		opacity: 1;
	}
	70% {
		transform: translateX(-60%);
		opacity: 1;
	}
	100% {
		transform: translateX(-60%);
		opacity: 0;
	}
}

:deep(.u-navbar__content__right) {
	background: white;
}
</style>