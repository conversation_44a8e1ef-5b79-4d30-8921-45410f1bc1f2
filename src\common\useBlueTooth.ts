// BlueTooth.ts
import { ref, reactive, onMounted, onUnmounted, watch } from 'vue';

// 蓝牙设备类型
interface BluetoothDevice {
    deviceId: string;
    name: string;
    RSSI: number;
    advertisData?: any;
    id?: string,
    lastId?: string
}

// 蓝牙服务类型
interface BluetoothService {
    uuid: string;
    isPrimary: boolean;
}

// 蓝牙特征值类型
interface BluetoothCharacteristic {
    uuid: string;
    properties: {
        read: boolean;
        write: boolean;
        notify: boolean;
        indicate: boolean;
    };
}

// 蓝牙状态类型
interface BluetoothState {
    isAvailable: boolean;     // 蓝牙适配器是否可用
    isConnected: boolean;     // 是否已连接到设备
    isDiscovering: boolean;   // 是否正在搜索设备
    connectedDevice: BluetoothDevice | null;
    battery: number;
}
type stringKey = Record<string, string>

// 蓝牙设备名称
const DEVICE_NAME: stringKey = {
    BLOOD_PRESSURE: 'SPHY',  // 血压
    THERMOMETER: 'ThermometerSplinched', // 体温
    BLOOD_OXYGEN: 'SPO2', // 血氧
    BLOOD_PRESSURE_AND_OXYGEN: 'HYO2' // 血压血氧一体机
}

// 服务UUID
const SERVICE_UUID_MAP: stringKey = {
    BLOOD_PRESSURE: '00001810-0000-1000-8000-00805f9b34fb',            // 血压计
    THERMOMETER: '00001809-0000-1000-8000-00805f9b34fb',               // 体温计
    BLOOD_OXYGEN: '00001822-0000-1000-8000-00805f9b34fb',              // 血氧计
    BLOOD_PRESSURE_AND_OXYGEN: '00001810-0000-1000-8000-00805f9b34fb', // 血压血氧一体机
    BATTERY: '0000180f-0000-1000-8000-00805f9b34fb'                    // 电池
}
//特征值UUID
const CHARACTERISTIC_UUID_MAP: stringKey = {
    BLOOD_PRESSURE: '00002a35-0000-1000-8000-00805f9b34fb',
    THERMOMETER: '00002a1c-0000-1000-8000-00805f9b34fb',
    BLOOD_OXYGEN: '00002a5e-0000-1000-8000-00805f9b34fb',
    BATTERY: '00002a19-0000-1000-8000-00805f9b34fb',
    BLOOD_PRESSURE_AND_OXYGEN: '00002a35-0000-1000-8000-00805f9b34fb'
}


// 蓝牙配置选项接口
interface BluetoothOptions {
    byNameConnect?: boolean;        // 是否按名称自动连接服务
    filterByDeviceName?: boolean;   // 是否只显示目标设备
    autoInit?: boolean;             // 是否自动初始化
    autoConnect?: boolean;          // 是否自动连接
}

// 蓝牙状态（响应式）
const state = reactive<BluetoothState>({
    isAvailable: false,
    isConnected: false,
    isDiscovering: false,
    connectedDevice: null,
    battery: 0
});

// 设备列表
const devices = ref<BluetoothDevice[]>([]);
// 服务列表
const services = ref<BluetoothService[]>([]);
// 特征值列表
const characteristics = ref<BluetoothCharacteristic[]>([]);

// 错误信息
const error = ref<string | null>(null);

// 加载状态
const isLoading = ref(false);

// 当前设备ID
const currentDeviceId = ref<string | null>(null);

// 当前服务ID
const currentServiceId = ref<string | null>(null);

// 当前特征值ID
// const currentCharacteristicId = ref<string | null>(null);

const SERVICE_UUID = ref<string>('');
const CHARACTERISTIC_UUID = ref<string>('');

const isbyNameConnect = ref<boolean>(false);
const isFilterByDeviceName = ref<boolean>(false);
const bluetoothPollingTimer = ref<any>(null);

// 蓝牙数据监听相关
interface MeasurementResult {
    systolic: number;      // 收缩压
    diastolic: number;     // 舒张压
    heartRate: number;     // 心率
    heartStatus: number;   // 心跳状态
    errorCode: number;     // 技术报警值
    timestamp: number;     // 时间戳
}


// 统一回调数据接口
export interface BluetoothCallbackData {
    deviceName: string;
    deviceId: string,
    packType: number;
    len: number;
    data: any;
}

// 回调函数类型定义
type BluetoothDataCallback = (callbackData: BluetoothCallbackData) => void;

// 回调函数存储
const bluetoothDataCallbacks = ref<BluetoothDataCallback[]>([]);

// 导出蓝牙 Hook
export default function BlueTooth(options: BluetoothOptions = {}) {
    // 解构参数并设置默认值
    const {
        byNameConnect = true,
        filterByDeviceName = true,
        autoInit = true,
        autoConnect = true
    } = options;

    isbyNameConnect.value = byNameConnect;
    isFilterByDeviceName.value = filterByDeviceName;
    

    // 打开蓝牙适配器
    const _openBluetoothAdapter = (): Promise<any> => {
        return new Promise((resolve, reject) => {
            uni.openBluetoothAdapter({
                success: res => {

                    if (bluetoothPollingTimer.value) {
                        clearInterval(bluetoothPollingTimer.value);
                        bluetoothPollingTimer.value = null;
                    }

                    resolve(res)
                },
                fail: res => {
                    console.log('打开蓝牙适配器失败', res)

                    if (res.code === 10001) {
                        clearInterval(bluetoothPollingTimer.value);
                        bluetoothPollingTimer.value = setInterval(async () => {
                            await initBluetooth();
                        }, 2000)
                    }

                    // if (res.code === -1 || res.errMsg.indexOf('fail not init') != -1) {
                    //     resolve({
                    //         available: false,
                    //         discovering: false
                    //     })
                    // }
                    reject(res)
                }
            });
        });
    }

    // 获取蓝牙适配器状态
    const _getBluetoothAdapterState = (): Promise<any> => {
        return new Promise((resolve, reject) => {
            uni.getBluetoothAdapterState({
                success: resolve,
                fail: res => {
                    if (res.code === 10000 || res.errMsg.indexOf('fail not init') != -1) {
                        resolve({
                            available: false,
                            discovering: false
                        })
                    }
                    reject(res)
                }
            });
        });
    }

    // 获取已配对设备
    const _getBluetoothDevices = (): Promise<any> => {
        return new Promise((resolve, reject) => {
            uni.getBluetoothDevices({
                success: res => {
                    const devices = res.devices
                    resolve(devices)
                },
                fail: res => {
                    resolve([])
                }
            });
        });
    }

    // 写入特征值
    const _writeBLECharacteristicValue = (deviceId: string, serviceId: string, characteristicId: string, value: any[]): Promise<any> => {
        return new Promise((resolve, reject) => {
            uni.writeBLECharacteristicValue({
                deviceId,
                serviceId,
                characteristicId,
                value,
                success: resolve,
                fail: reject
            });
        });
    }

    const _notifyBLECharacteristicValueChange = (deviceId: string, serviceId: string, characteristicId: string, state: boolean): Promise<any> => {

        console.log('启用通知', deviceId, serviceId, characteristicId, state)

        return new Promise((resolve, reject) => {
            uni.notifyBLECharacteristicValueChange({
                deviceId,
                serviceId,
                characteristicId,
                state,
                success: resolve,
                fail: reject
            });
        });
    }

    const _readBLECharacteristicValue = (deviceId: any, serviceId: string, characteristicId: string, packet: any): Promise<any> => {
        return new Promise((resolve, reject) => {
            uni.readBLECharacteristicValue({
                deviceId,
                serviceId,
                characteristicId,
                packet,
                success: resolve,
                fail: reject
            });
        });
    }

    // 创建蓝牙连接
    const _createBLEConnection = (deviceId: string): Promise<any> => {
        return new Promise((resolve, reject) => {
            uni.createBLEConnection({
                deviceId,
                success: resolve,
                fail: (err) => {
                    if (err.code === -1 || err.errMsg.indexOf('already connect') != -1) {
                        resolve(err)
                    }
                    reject(err)
                }
            });
        });
    }



    // 初始化蓝牙适配器
    const initBluetooth = async () => {
        try {
            // isLoading.value = true;
            error.value = null;

            // 检查蓝牙适配器可用性
            const res = await _getBluetoothAdapterState();
            console.log('步骤1', res)

            state.isAvailable = res.available;      // 蓝牙适配器是否可用
            state.isDiscovering = res.discovering;    // 是否正在搜索设备

            // 如果蓝牙未打开，尝试打开
            if (!state.isAvailable) {
                await _openBluetoothAdapter();
                console.log('步骤2')
                state.isAvailable = true;
                // state.isAvailable = true;
            }

            // 监听蓝牙适配器状态变化
            uni.onBluetoothAdapterStateChange((res) => {
                console.log('适配器变化', res)
                state.isAvailable = res.available;        // 蓝牙适配器是否可用
                state.isDiscovering = res.discovering;    // 蓝牙适配器是否处于搜索状态

                // 没有开启适配器
                if (!res.available) {

                    // 清除连接
                    state.isConnected = false

                    // 在搜寻，清除搜寻
                    if (res.discovering) {
                        state.isDiscovering = false
                    }
                }
            });

            // 监听蓝牙连接状态变化
            uni.onBLEConnectionStateChange((res) => {
                console.log('连接变化', res)
                if (!res.connected) {
                    state.connectedDevice = null;
                    currentDeviceId.value = null;
                    services.value = [];
                    characteristics.value = [];

                    // 连接断开,开启搜寻
                    if (autoConnect) {
                        startDiscovery()
                    }
                }
                state.isConnected = res.connected;    // 是否已连接到设备
            });

            // 监听设备发现事件
            uni.onBluetoothDeviceFound((res) => {
                res.devices.forEach(device => {
                    if (!device.name) return;

                    // 检查是否是目标设备
                    const isTargetDevice = Object.values(['HYO2', 'SPHY']).some(name =>
                        device.name == name
                    );

                    // 如果设备已存在，不重复添加
                    const deviceExists = devices.value.find(d => d.deviceId === device.deviceId);

                    // 根据过滤设置决定是否添加设备
                    const shouldAddDevice = isFilterByDeviceName.value ? isTargetDevice : true;

                    if (shouldAddDevice && !deviceExists) {
                        devices.value.push({
                            deviceId: device.deviceId,
                            name: device.name,
                            RSSI: device.RSSI,
                            advertisData: device.advertisData
                        });


                        if (isTargetDevice) {
                            console.log('发现目标设备:', device.name);

                            connectToDevice(device.deviceId)
                        } else {
                            console.log('发现其他设备:', device.name);
                        }
                    }
                });
            });

            // 获取已配对设备
            devices.value = await _getBluetoothDevices();
        } catch (err) {
            console.log('什么鬼', err)
            handleError(err);
        } finally {
            isLoading.value = false;
        }
    };

    const uninitBluetooth = async () => {
        try {
            isLoading.value = true;
            error.value = null;

            if (state.isDiscovering) {
                stopDiscovery();
            }

            if (state.isConnected && currentDeviceId.value) {
                disconnectDevice();
            }

            // uni.offBluetoothAdapterStateChange();
            // uni.offBluetoothDeviceFound();

            await uni.closeBluetoothAdapter();

            state.isAvailable = false;
            state.isDiscovering = false;
            state.isConnected = false;
        } catch (err) {
            handleError(err);
        } finally {
            isLoading.value = false;
        }
    };

    // 打开蓝牙适配器
    const openBluetoothAdapter = async () => {
        try {
            isLoading.value = true;
            error.value = null;
            await _openBluetoothAdapter();
            state.isAvailable = true;
            return true;
        } catch (err) {
            handleError(err);
            return false;
        } finally {
            isLoading.value = false;
        }
    };

    // 开始搜索设备
    const startDiscovery = async () => {
        try {
            if (!state.isAvailable) {
                throw new Error('请先打开蓝牙适配器，搜索时');
            }

            isLoading.value = true;
            error.value = null;
            devices.value = [];

            // 有监听，不需要
            // state.isDiscovering = true;
            // console.log('正在搜索')

            await uni.startBluetoothDevicesDiscovery();
            return true;
        } catch (err) {
            handleError(err);
            state.isDiscovering = false;
            return false;
        } finally {
            isLoading.value = false;
        }
    };

    // 停止搜索设备
    const stopDiscovery = async () => {
        try {
            isLoading.value = true;
            error.value = null;
            await uni.stopBluetoothDevicesDiscovery();
            // 有监听，可以不需要
            // state.isDiscovering = false;
            return true;
        } catch (err) {
            handleError(err);
            return false;
        } finally {
            isLoading.value = false;
        }
    };


    const setDeviceMTU = (): void => {
        if (!currentDeviceId.value) return;

        uni.setBLEMTU({
            deviceId: currentDeviceId.value,
            mtu: 64,
            success: () => {
                console.log("MTU设置成功");
            },
            fail: (err) => {
                console.error("MTU设置失败:", err);
            },
        });
    };

    // 连接到设备
    const connectToDevice = async (deviceId: string) => {
        try {
            if (!state.isAvailable) {
                throw new Error('请先打开蓝牙适配器，连接时');
            }

            isLoading.value = true;
            error.value = null;

            // 停止搜索
            if (state.isDiscovering) {
                await stopDiscovery();
            }

            // 连接设备
            await _createBLEConnection(deviceId);
            state.isConnected = true;
            currentDeviceId.value = deviceId;

            console.log('连接成功')

            // 查找设备信息
            const device = devices.value.find(d => d.deviceId === deviceId);
            if (device) {
                device.id = device.deviceId?.replace(/:/g, '')
                device.lastId = device.id.slice(-4)
                state.connectedDevice = device;
            }

            // 通过设备名字，获取服务、特征值
            if (isbyNameConnect.value) {
                connectToService()

                // 清空设备列表
                devices.value = []
            }

            // if (services.value.length) {
            //     const targetService = services.value.find(
            //         (item: { uuid: string }) => item.uuid === SERVICE_UUID
            //     );
            // }


            return true;
        } catch (err) {
            handleError(err);
            state.isConnected = false;
            currentDeviceId.value = null;
            return false;
        } finally {
            isLoading.value = false;
        }
    };

    // 按照设备名称，连接服务，特征值
    const connectToService = async () => {
        try {
            if (!state.isConnected || !currentDeviceId.value) {
                throw new Error('请先连接到设备');
            }

            isLoading.value = true;
            error.value = null;

            // 获取服务列表
            await getServices();
            if (!services.value.length) {
                throw new Error('未找到可用服务');
            }

            Object.keys(DEVICE_NAME).forEach((key) => {
                if (DEVICE_NAME[key] === state.connectedDevice?.name) {
                    SERVICE_UUID.value = SERVICE_UUID_MAP[key].toLocaleUpperCase();
                    CHARACTERISTIC_UUID.value = CHARACTERISTIC_UUID_MAP[key].toLocaleUpperCase();
                }
            })

            // 查找目标服务
            const targetService = services.value.find(
                (item: { uuid: string }) => item.uuid == SERVICE_UUID.value
            );
            console.log(services.value)
            if (!targetService) {
                throw new Error('未找到目标服务-1');
            }

            // 设置MTU并获取特征值
            // setDeviceMTU();

            // 获取特征值
            const characteristicList = await getCharacteristics(targetService.uuid);
            if (!characteristicList.length) {
                throw new Error('未找到可用特征值');
            }

            // 查找目标特征值
            console.log(characteristicList)
            const targetCharacteristic = characteristicList.find(
                (item: { uuid: string }) => item.uuid == CHARACTERISTIC_UUID.value
            );
            if (!targetCharacteristic) {
                throw new Error('未找到目标特征值');
            }

            // 启用通知
            await _notifyBLECharacteristicValueChange(
                currentDeviceId.value,
                targetService.uuid,
                targetCharacteristic.uuid,
                true
            );
            
            await batteryListen()

            // 监听数据变化
            setupConnectionListeners()


            setTimeout(() => {
                getBattery()
            }, 3000);


            return true;
        } catch (err) {
            handleError(err);
            return false;
        } finally {
            isLoading.value = false;
        }
    }
	const delay = (ms : number | undefined) => {
		return new Promise((resolve) => {
			setTimeout(resolve, ms);
		});
	}

    // 电池启动获取
    const batteryListen = async () => {
        if (!state.isConnected || !currentDeviceId.value) {
            throw new Error('请先连接到设备');
        }
        // const characteristicList = await getCharacteristics(SERVICE_UUID_MAP.BATTERY);
        // console.log(characteristicList)

        // 电量监听
        await _notifyBLECharacteristicValueChange(
            currentDeviceId.value,
            SERVICE_UUID_MAP.BATTERY,
            CHARACTERISTIC_UUID_MAP.BATTERY,
            true
        );
    }

    const getBattery = async (): Promise<any> => {

        await delay(3000);
        try {
            const packetArray = assembleData(PRESSURE_PACKET_TYPE.BATTERY_INFO)

            await _readBLECharacteristicValue(currentDeviceId.value, SERVICE_UUID_MAP.BATTERY, CHARACTERISTIC_UUID_MAP.BATTERY, packetArray)
            return true
        } catch (err) {
            handleError(err);
            return false
        }
    }

    // 断开设备连接
    const disconnectDevice = async () => {
        try {
            if (!state.isConnected || !currentDeviceId.value) {
                return true;
            }

            isLoading.value = true;
            error.value = null;

            await uni.closeBLEConnection({ deviceId: currentDeviceId.value });

            state.isConnected = false;
            state.connectedDevice = null;
            currentDeviceId.value = null;
            services.value = [];
            characteristics.value = [];

            return true;
        } catch (err) {
            handleError(err);
            return false;
        } finally {
            isLoading.value = false;
        }
    };

    // 获取服务列表（带重试机制）
    const getServices = async (maxRetries = 10, delay = 500): Promise<any> => {
        try {
            if (!state.isConnected || !currentDeviceId.value) {
                throw new Error('请先连接到设备');
            }

            isLoading.value = true;
            error.value = null;
            services.value = [];

            let retries = 0;
            let success = false;

            while (retries < maxRetries && !success) {
                try {
                    const res = await uni.getBLEDeviceServices({
                        deviceId: currentDeviceId.value
                    });

                    if (res.services.length > 0) {
                        services.value = res.services;
                        success = true;
                    } else {
                        retries++;
                        if (retries < maxRetries) {
                            await new Promise(resolve => setTimeout(resolve, delay));
                        }
                    }
                } catch (serviceErr) {
                    retries++;
                    if (retries < maxRetries) {
                        await new Promise(resolve => setTimeout(resolve, delay));
                    } else {
                        throw serviceErr;
                    }
                }
            }

            if (!success) {
                throw new Error(`获取服务失败，重试${maxRetries}次后仍无数据`);
            }

            return services.value;
        } catch (err) {
            handleError(err);
            return [];
        } finally {
            isLoading.value = false;
        }
    };

    // 获取特征值
    const getCharacteristics = async (serviceId: string) => {
        try {
            if (!state.isConnected || !currentDeviceId.value) {
                throw new Error('请先连接到设备');
            }

            isLoading.value = true;
            error.value = null;
            characteristics.value = [];

            const res = await uni.getBLEDeviceCharacteristics({
                deviceId: currentDeviceId.value,
                serviceId
            });

            characteristics.value = res.characteristics;
            currentServiceId.value = serviceId;
            return characteristics.value;
        } catch (err) {
            handleError(err);
            return [];
        } finally {
            isLoading.value = false;
        }
    };

    // 错误处理
    const handleError = (err: any) => {
        console.error('蓝牙错误:', err);

        if (err.errCode) {
            switch (err.errCode) {
                case 10000:
                    error.value = '未初始化蓝牙适配器';
                    break;
                case 10001:
                    error.value = '当前蓝牙适配器不可用';
                    break;
                case 10002:
                    error.value = '没有找到指定设备';
                    break;
                case 10003:
                    error.value = '连接失败';
                    break;
                case 10004:
                    error.value = '没有找到指定服务';
                    break;
                case 10005:
                    error.value = '没有找到指定特征值';
                    break;
                case 10006:
                    error.value = '当前连接已断开';
                    break;
                case 10007:
                    error.value = '当前特征值不支持此操作';
                    break;
                case 10008:
                    error.value = '其余所有系统上报的异常';
                    break;
                case 10009:
                    error.value = '系统版本低于 4.3，不支持 BLE';
                    break;
                case 10012:
                    error.value = '连接超时';
                    break;
                case 10013:
                    error.value = '重复连接';
                    break;
                default:
                    error.value = `未知错误: ${err.errMsg || err.message}`;
            }
        } else {
            error.value = err.message || '未知蓝牙错误';
        }
    };


    // 统一触发回调的辅助函数
    const triggerBluetoothCallback = (packType: number, len: number, data: any) => {
        const callbackData: BluetoothCallbackData = {
            deviceName: state.connectedDevice?.name || '未知设备',
            deviceId: state.connectedDevice?.id || '',
            packType,
            len,
            data
        };

        bluetoothDataCallbacks.value.forEach(callback => {
            try {
                callback(callbackData);
            } catch (error) {
                console.error('蓝牙数据回调执行出错:', error);
            }
        });
    };

    // 设置连接监听器
    const setupConnectionListeners = () => {

        let batteryDataBuffers = new Uint8Array(0);
        uni.onBLECharacteristicValueChange((res: any) => {
            const view = new DataView(res.value);

            const deviceBuffer = batteryDataBuffers || new Uint8Array(0);
            const newData = new Uint8Array(res.value);
            let combinedBuffer = new Uint8Array(deviceBuffer.length + newData.length);
            combinedBuffer.set(deviceBuffer, 0);
            combinedBuffer.set(newData, deviceBuffer.length);

            console.log(combinedBuffer)


            // 验证包头和包尾
            if (view.getUint8(0) !== 0x5B) return;

            const length = view.getUint8(1);
            const type = view.getUint8(2);

            if (view.getUint8(3 + length) !== 0xB5) return;

            console.log(`类型：${type}, 长度：${length}, 数据：${view}`)
            let returnData: any = ''

            // 根据类型处理数据
            switch (type) {
                case 0x01: // 启动测量回复或袖带压  Hex(十六进制)  10进制 = 1
                    if (length === 1) {
                        // 启动测量回复
                        const result = view.getUint8(3);
                        returnData = { result };

                        if (result === 0) {
                            console.log('测量启动成功');
                        } else if (result === 0xFF) {
                            console.log('启动失败，设备正忙')
                        } else if (result === 0xFE) {
                            console.log('启动失败，功能不支持')
                        }

                    } else if (length === 2) {
                        // 袖带压数据
                        const pressure = view.getUint16(3, true);
                        returnData = {
                            pressure,
                            timestamp: Date.now()
                        };

                        console.log('返回袖带压数据：', returnData)
                    } else if (length === 3) {
                        // 电池信息
                        const voltage = view.getUint16(3, true);
                        const percentage = view.getUint8(5);
                        returnData = {
                            voltage,
                            percentage
                        };
                        state.battery = percentage
                        console.log('电池信息:', returnData)
                    }
                    break;
                case 0x02:
                    if (length === 8) {
                        // 测量结果
                        returnData = {
                            systolic: view.getUint16(3, true),
                            diastolic: view.getUint16(5, true),
                            heartRate: view.getUint16(7, true),
                            heartStatus: view.getUint8(9),
                            errorCode: view.getUint8(10),
                            timestamp: Date.now()
                        };
                        console.log('测量结果:', returnData)
                    }
                    break;

                case 0x03: // 时间数据   十进制 = 3
                    if (length === 4) {
                        const timestamp = view.getUint32(3, true);
                        returnData = {
                            timestamp,
                            date: new Date(timestamp * 1000)
                        };
                        console.log('设备时间:', returnData.date);
                    }
                    break;

                case 0x04: // 测量结果  十进制 = 4
                    if (length === 8) {
                        returnData = {
                            systolic: view.getUint16(3, true),
                            diastolic: view.getUint16(5, true),
                            heartRate: view.getUint16(7, true),
                            heartStatus: view.getUint8(9),
                            errorCode: view.getUint8(10),
                            timestamp: Date.now()
                        };
                        console.log('测量结果:', returnData)
                    }
                    break;
            }

            triggerBluetoothCallback(type, length, returnData);
        });
    }
    
    const assembleData = (type: number, data: ArrayBuffer = new ArrayBuffer(0)) => {

        const dataLength = data.byteLength;
        const packet = new ArrayBuffer(4 + dataLength); // 包头+长度+类型+数据+包尾
        const view = new DataView(packet);

        let offset = 0;
        view.setUint8(offset++, 0x5B); // 包头
        view.setUint8(offset++, dataLength); // 数据长度
        view.setUint8(offset++, type); // 包类型

        // 复制数据
        if (dataLength > 0) {
            const dataView = new DataView(data);
            for (let i = 0; i < dataLength; i++) {
                view.setUint8(offset++, dataView.getUint8(i));
            }
        }

        view.setUint8(offset, 0xB5); // 包尾

        // 将 ArrayBuffer 转换为数组
        const packetArray = Array.from(new Uint8Array(packet));

        return packetArray;
    };

    const sendPacket = async (type: number, data: ArrayBuffer = new ArrayBuffer(0)): Promise<void> => {
        if (!state.isConnected || !currentDeviceId.value) {
            throw new Error('设备未连接');
        }

        // 将 ArrayBuffer 转换为数组
        const packetArray = assembleData(type, data)

        try {

            console.log('写入特征值：', currentDeviceId.value,
                SERVICE_UUID.value,
                CHARACTERISTIC_UUID.value,
                packetArray)

            await _writeBLECharacteristicValue(
                currentDeviceId.value,
                SERVICE_UUID.value,
                CHARACTERISTIC_UUID.value,
                packetArray
            );
        } catch (error) {
            console.error('发送数据失败:', error);
            throw error;
        }
    }

    // 组件挂载时初始化蓝牙适配器
    onMounted(async () => {
        // 根据 autoInit 参数决定是否自动初始化蓝牙
        if (autoInit) {
            console.log('自动初始化蓝牙');
            initBluetooth();
        }
    });

    onUnmounted(async () => {
        // 销毁蓝牙
        console.log('销毁蓝牙')
        uninitBluetooth()
    })












    // ============================================血压函数==============================================
    // 血压
    const enum PRESSURE_PACKET_TYPE {
        START_MEASUREMENT = 0x01,    // 启动测量
        STOP_MEASUREMENT = 0x02,     // 停止测量
        TIME_QUERY = 0x03,           // 查询/设置时间
        MEASUREMENT_RESULT = 0x04,   // 查询/上报测量结果
        CUFF_PRESSURE = 0x01,        // 袖带压(notify)
        BATTERY_INFO = 0x01          // 电池信息(notify)
    }

    // 启动测量
    const pressure_startMeasurement = async (): Promise<boolean> => {
        try {
            isLoading.value = true;
            console.log('启动测量')
            await sendPacket(PRESSURE_PACKET_TYPE.START_MEASUREMENT);

            isLoading.value = false;
            return true;
        } catch (error) {
            handleError('启动测量失败');

            isLoading.value = false;
            return false;
        }
    }

    // 停止测量
    const pressure_stopMeasurement = async (): Promise<boolean> => {
        try {
            isLoading.value = true;

            await sendPacket(PRESSURE_PACKET_TYPE.STOP_MEASUREMENT);

            isLoading.value = false;
            return true;
        } catch (error) {
            handleError('停止测量失败');

            isLoading.value = false;
            return false;
        }
    }

    // 查询测量结果
    const pressure_queryMeasurementResult = async (): Promise<void> => {
        try {
            isLoading.value = true;

            await sendPacket(PRESSURE_PACKET_TYPE.MEASUREMENT_RESULT);

            isLoading.value = false;
        } catch (error) {
            handleError('查询测量结果失败');

            isLoading.value = false;
        }
    }

    // ============================================WiFi配置函数==============================================

    // WiFi配置包类型
    enum WIFI_PACKET_TYPE {
        ADD_WIFI = 0x08              // 添加WiFi配置
    }

    // 发送WiFi配置
    const wifi_sendConfig = async (ssid: string, password: string): Promise<boolean> => {
        try {
            isLoading.value = true;
            console.log('发送WiFi配置:', { ssid, password });

            // 构建WiFi配置数据包
            // 数据包含2个字符串，都以0结尾
            // 第一个字符串是SSID，第二个是密码

            // 将字符串转换为字节数组
            const ssidBytes = new TextEncoder().encode(ssid);
            const passwordBytes = new TextEncoder().encode(password);

            // 计算总数据长度（包括两个null终止符）
            const dataLength = ssidBytes.length + 1 + passwordBytes.length + 1;

            // 创建数据缓冲区
            const dataBuffer = new ArrayBuffer(dataLength);
            const dataView = new Uint8Array(dataBuffer);

            let offset = 0;

            // 写入SSID
            dataView.set(ssidBytes, offset);
            offset += ssidBytes.length;
            dataView[offset++] = 0; // null terminator

            // 写入密码
            dataView.set(passwordBytes, offset);
            offset += passwordBytes.length;
            dataView[offset++] = 0; // null terminator

            console.log('WiFi数据包构建完成:', {
                ssid,
                password,
                dataLength,
                hexData: Array.from(dataView).map(b => '0x' + b.toString(16).padStart(2, '0')).join(' ')
            });

            // 发送数据包
            await sendPacket(WIFI_PACKET_TYPE.ADD_WIFI, dataBuffer);

            isLoading.value = false;
            return true;
        } catch (error) {
            console.error('发送WiFi配置失败:', error);
            handleError('发送WiFi配置失败');

            isLoading.value = false;
            return false;
        }
    }

    // 查询时间
    const pressure_queryTime = async (): Promise<void> => {
        try {
            isLoading.value = true;
            await sendPacket(PRESSURE_PACKET_TYPE.TIME_QUERY);

            isLoading.value = false;
        } catch (error) {
            handleError('查询时间失败');
            isLoading.value = false;
        }
    }

    // 设置时间
    const pressure_setTime = async (timestamp: number): Promise<void> => {
        try {
            isLoading.value = true;

            const data = new ArrayBuffer(4);
            const view = new DataView(data);
            view.setUint32(0, timestamp, true); // 小端序
            await sendPacket(PRESSURE_PACKET_TYPE.TIME_QUERY, data);
            isLoading.value = false;
        } catch (error) {
            handleError('设置时间失败');
            isLoading.value = false;
        }
    }


    // 统一回调管理方法
    const onBluetoothData = (callback: BluetoothDataCallback) => {
        bluetoothDataCallbacks.value.push(callback);
        // 返回取消函数
        return () => {
            const index = bluetoothDataCallbacks.value.indexOf(callback);
            if (index > -1) {
                bluetoothDataCallbacks.value.splice(index, 1);
            }
        };
    };

    // 清除所有回调
    const clearAllCallbacks = () => {
        bluetoothDataCallbacks.value = [];
    };

    watch(() => state.isAvailable, status => {
        console.log('蓝牙状态变化', status)
        if (status && autoConnect) {
            startDiscovery()
        }
    })

    // 返回所有状态和方法
    return {
        // 状态
        state,
        devices,
        services,
        characteristics,
        error,
        isLoading,
        currentDeviceId,
        currentServiceId,

        // 蓝牙数据监听 - 统一回调方法
        onBluetoothData,
        clearAllCallbacks,

        // 方法
        initBluetooth,
        uninitBluetooth,
        openBluetoothAdapter,
        startDiscovery,
        stopDiscovery,
        connectToDevice,
        disconnectDevice,
        getServices,
        getCharacteristics,

        // 血压方法 PRESSURE
        pressure_startMeasurement,
        pressure_stopMeasurement,

        // WiFi配置方法
        wifi_sendConfig
    };
}


/**
 * 使用说明
 *
 * 基本用法：
 * const { state, devices, connectToDevice, startDiscovery, pressure_startMeasurement } = BlueTooth();
 *
 * 带配置选项的用法：
 * const { state, devices, connectToDevice } = BlueTooth({
 *   byNameConnect: true,      // 是否按名称自动连接服务，默认 true
 *   filterByDeviceName: true, // 是否只显示目标设备，默认 true
 *   autoInit: true            // 是否自动初始化，默认 true
 * });
 *
 * 全局共享实例用法（推荐）：
 * const {
 *   state,
 *   devices,
 *   pressure_startMeasurement,
 *   onBluetoothData,
 *   clearAllCallbacks
 * } = useBlueToothData();
 *
 * 数据监听用法：
 *
 * // 方式2: 使用统一回调函数
 * const unsubscribe = onBluetoothData((callbackData) => {
 *   console.log('蓝牙数据:', callbackData);
 *   // callbackData 格式:
 *   // {
 *   //   deviceName: '设备名称',
 *   //   包类型: '测量结果' | '袖带压' | '电池信息' | '启动测量回复' | '时间数据',
 *   //   长度: 数据长度,
 *   //   data: 具体数据对象
 *   // }
 * });
 *
 * // 取消监听
 * unsubscribe();
 *
 * 连接蓝牙（设备id）
 * connectToDevice(device.deviceId)
 *
 * 启动测量
 * pressure_startMeasurement()
 *
 * 启动搜索附近蓝牙
 * startDiscovery()
 */