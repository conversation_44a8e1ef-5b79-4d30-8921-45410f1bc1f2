<template>
    <view>
        <view class="text-[34rpx] py-[30rpx] flex justify-between items-center">
            <text style="text-shadow: 3px 3px 3px #b9b9b9;">诊室测量</text>
            <view v-if="dataList.length && props.steps === 50" @click="onClickSure" class="w-[220rpx] py-[16rpx] text-center rounded-[40rpx] bg-[#237FC2] color-white">完成测量</view>
            <uni-icons v-if="props.steps > 50" custom-prefix="iconfont" type="icon-dagou"size="28" color="#1ABA62"></uni-icons>
        </view>

        <view v-if="props.steps === 50">
            <view v-if="dataList.length">
                <view v-for="item in dataList" class="p-[30rpx] pb-[40rpx] bg-white mb-[30rpx] text-[32rpx] rounded-[8rpx]">
                    <dataItem :item="item"></dataItem>
                </view>
            </view>
            <view v-else class="p-[50rpx] bg-white">
                <up-empty mode="list" text="暂无测量" icon="/static/img/empty1.png" height="83" width="145"></up-empty>
            </view>
        </view>
        <view v-else class="py-[40rpx] px-[30rpx] bg-white text-[32rpx] text-center rounded-[16rpx]">
            <text class="color-[#1B9ADC] underline decoration-[#1B9ADC]" @click="onClickData">点击查看数据</text>
        </view>
    </view>
</template>
<script setup lang="ts">
/**
 * 二次诊室测量
 */
import { ref, reactive, onMounted, onBeforeUnmount, computed, nextTick } from 'vue';
import moment from 'moment';

import { queryPatientMeasureRecord, updateMeasureStatus, unbindDevice } from '@/common/api/task';

import dataItem from './dataItem.vue'


const props = defineProps({
    patient: {
        type: Object,
        default: () => {},
    },
    device: {
        type: Object,
        default: () => {},
    },
    steps: {
        type: Number,
        default: 0
    }
})

const dataList = ref<any[]>([])

// 获取测量记录
function queryList() {
    const params = {
        bpMeasureType: 50, // 二次诊室测量
        patientId: props.patient.patientId
    }
    queryPatientMeasureRecord(params).then(res => {
        dataList.value = res.data || []
    })
}

const onClickData = () => {
    const patient = encodeURIComponent(JSON.stringify(props.patient))
    
    uni.navigateTo({
        url: '/pages/home/<USER>' + patient,
        animationType: "slide-in-right",
        animationDuration: 300,

    })
}

const finished = async () => {
    const finishDevice: any = await unbindDevice({deviceNo: props.device.deviceNo, patientId: props.patient.patientId})
    const finishStatus: any = await updateMeasureStatus({ measureStatus: 60, patientId: props.patient.patientId })

    if (finishDevice.success && finishStatus.success) {
        props.patient.executeStatus = 60
        uni.showToast({
            title: '完成成功！',
            icon: 'success'
        })
        uni.navigateBack({delta: 1})
        return
    }
    uni.showToast({
        title: '无法完成！',
        icon: 'error'
    })
}

const onClickSure = () => {
    uni.showModal({
        title: '提示',
        content: '是否完成测量，并解绑设备？',
        success: (res) => {
            if (res.confirm) {
                finished()
            }
        }
    })
}

onMounted(() => {
    queryList()
})

defineExpose({
    queryList
})
</script>