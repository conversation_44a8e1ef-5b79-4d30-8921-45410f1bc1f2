{
	"code": 0, //按照你们的约定使用业务code
	"data": [{
		"type": "1",
		"id": "1",
		"typeName": "血压",
		"icon": "@/static/clinic-introduction.png",
		"datetime": "11-20 10:35",
		"require": ["餐前"],
		"measureValue": "135/83",
		"measureValueUnit": "mmHg",
		"pulse": "80",
		"measureResultFlag": ""
	}, {
		"type": "2",
		"id": "2",
		"typeName": "血氧",
		"icon": "@/static/clinic-introduction.png",
		"datetime": "11-20 10:35",
		"require": ["餐前"],
		"measureValue": "10.8",
		"measureValueUnit": "%SpO2",
		"pulse": "",
		"measureResultFlag": ""
	}, {
		"type": "3",
		"id": "3",
		"typeName": "体温",
		"icon": "@/static/clinic-introduction.png",
		"datetime": "11-20 10:35",
		"require": [],
		"measureValue": "36.9",
		"measureValueUnit": "℃",
		"pulse": "",
		"measureResultFlag": ""
	}, {
		"type": "4",
		"id": "4",
		"typeName": "心率",
		"icon": "@/static/clinic-introduction.png",
		"datetime": "11-20 10:35",
		"require": [],
		"measureValue": "80",
		"measureValueUnit": "次/分",
		"pulse": "",
		"measureResultFlag": ""
	}, {
		"type": "5",
		"id": "5",
		"typeName": "脉率",
		"icon": "@/static/clinic-introduction.png",
		"datetime": "11-20 10:35",
		"require": [],
		"measureValue": "83",
		"measureValueUnit": "次/分",
		"pulse": "",
		"measureResultFlag": ""
	}, {
		"type": "6",
		"id": "6",
		"typeName": "体重",
		"icon": "@/static/clinic-introduction.png",
		"datetime": "11-20 10:35",
		"require": [],
		"measureValue": "-",
		"measureValueUnit": "kg",
		"pulse": "",
		"measureResultFlag": ""
	}, {
		"type": "7",
		"id": "7",
		"typeName": "血压",
		"icon": "@/static/clinic-introduction.png",
		"datetime": "11-20 10:35",
		"require": [],
		"measureValue": "-",
		"measureValueUnit": "mmol/L",
		"pulse": "",
		"measureResultFlag": ""
	}],
	"message": "请求成功"
}