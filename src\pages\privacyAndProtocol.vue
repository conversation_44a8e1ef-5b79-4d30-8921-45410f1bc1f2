<template>
	<view >
		<view class="popup-content">
			<view style="text-align: center;font-weight: bold;padding: 20rpx 0 28rpx;">{{ protocolInfo.title }}</view>
			<text class="text">{{ protocolInfo.content }}</text>
			<!-- <view style="margin-top: 20rpx;" class="xx-button" @click="onConfirmClick">确认</view> -->
		</view>
	</view>
</template>

<script setup lang="ts">
	import { onMounted, ref, reactive } from "vue";
	import { onLoad, onShow } from "@dcloudio/uni-app";
	/* import {
		indexOutpatientServices,
		physicalExamination,
		convenienceService,
	} from "../model/module";
	import clinicIntroductionIcon from "@/static/clinic-introduction.png";
	import companyIntroduction from "@/static/company-introduction.png"
	import rouetIcon from "@/static/route.png";
	import doctorIcon from "@/static/doctor.png"; */

	import protocolData from './protocol.js';
	const protocolInfo = reactive({ title: '', content: protocolData.content }); 

	onLoad((option : any) => {
		//{"type":"1"}
		console.log('PRIVACY【onLoad】：页面加载完成', JSON.stringify(option));
		uni.setNavigationBarTitle({ title: option.type == 1 ? '用户协议' : '隐私政策' });
	})

	onShow(() => {
		console.log("PRIVACY【onShow】：页面重新可见");


	});

	const onConfirmClick = () => {
		//返回
		uni.navigateBack({})
	}
</script>

<style scoped lang="scss">
	.popup-content {
		text-align: left;
		overflow: auto;
		padding: 20rpx;

		.text {
			text-indent: 2rem;
		}
	}
</style>