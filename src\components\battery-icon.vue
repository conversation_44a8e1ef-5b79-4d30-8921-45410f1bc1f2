<template>
  <view class="battery-container">
    <view class="battery-icon" :class="batteryClass" :style="{ width: width, height: height }">
      <view class="battery-level" :style="{ width: batteryLevel + '%' }"></view>
      <view class="battery-tip"></view>
    </view>
    <text class="battery-text" :style="{ color: batteryColor }">{{ batteryLevel }}%</text>
  </view>
</template>

<script>
export default {
  props: {
    batteryLevel: {
      type: Number,
      required: true,
      validator: value => value >= 0
    },
    width: {
      type: String,
      default: '84rpx'
    },
    height: {
      type: String,
      default: '46rpx'
    }
  },
  computed: {
    effectiveBatteryLevel() {
      return Math.min(this.batteryLevel, 100);
    },
    batteryColor() {
      return this.effectiveBatteryLevel < 20 ? 'red' : '#000';
    },
    batteryClass() {
      return {
        'low-battery': this.effectiveBatteryLevel < 20,
        'high-battery': this.effectiveBatteryLevel >= 20
      };
    }
  }
}
</script>

<style scoped>
.battery-container {
position: relative;
}

.battery-icon {
 /* width: 84rpx;
  height: 46rpx; */
  border: 2px solid #ccc;
  border-radius: 5px;
  position: relative;
  background-color: #f0f0f0;
  padding: 2px;
}

.battery-level {
  height: 100%;
  border-radius: 3px;
  transition: width 0.3s ease;
  background-color: transparent;
}

.low-battery .battery-level {
  background-color: red;
}

.high-battery .battery-level {
  background-color: #00FF00;
}

.battery-text {
	position: absolute;
	 top: 50%;
	left: 0;
	transform: translateY(-50%);
  font-size: 14px;
  margin-left: 5px;
  font-weight: 500;
}

.battery-tip {
  width: 12rpx;
  height: 20rpx;
  background-color: #ccc;
  position: absolute;
  right: -6px;
  top: 50%;
  transform: translateY(-50%);
  border-radius: 2px;
}
</style> 