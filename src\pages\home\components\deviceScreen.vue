<template>
    <view class="bg-white rounded-[16rpx] p-[30rpx] pt-[50rpx] pl-[40rpx] flex relative">
        <view class="text-[32rpx] flex-1 flex flex-col justify-center">
            <view>
                <text class="pr-[16rpx]">编号：{{ deviceNo }}</text>
                <text class="pr-[16rpx]">电量：<text :class="{'color-[red]': parseInt(capacityPercent) <= 25}">{{ capacityPercent }}</text></text>
                <text class="online" :class="'online' + onlineState">{{ onlineArr[onlineState] }}</text>
            </view>
            <!-- <images src="/src/static/img/loading.png" class="w-[48rpx] h-[48rpx] object-contain"></images> -->
            <view class="w-full h-[1px] bg-[#d9d9d9] mt-[12rpx] mb-[24rpx]"></view>
            <view class="b-tn not-tn" v-if="showBtnStatus == 0">等待联网</view>
            <view class="b-tn" v-else-if="showBtnStatus == 1" @click="onClickMeasure">去测量</view>
            <view class="b-tn connect-tn" v-else-if="showBtnStatus == 2">
            <text>测量中</text>
            <view class="flex flex-row">
				<view class="dot-loader" style="background-color: #1ABA62;"></view>
				<view class="dot-loader dot-loader--2" style="background-color: #1ABA62;"></view>
				<view class="dot-loader dot-loader--3" style="background-color: #1ABA62;"></view>
			</view>
            </view>
        </view>
        <view class="ml-[20rpx] w-[100rpx] flex justify-center">
            <image :src="images.HYO2" class="w-[100rpx] h-[100rpx] object-contain" mode="widthFix" />
        </view>
        <view :class="[showMeasureBox ? 'h-[260rpx] border-t border-t-[1px] border-t-dotted border-t-[#1aba62] shadow-[rgba(0,_0,_0,_0.1)_0px_5px_10px_0px]' : ' h-[0]']" 
        class="absolute left-[0] top-[234rpx] w-full bg-[white] z-4 px-[20rpx] pt-[6rpx] box-border overflow-hidden" 
        style="border-radius: 0 0 16rpx 16rpx;transition: height 0.5s ease-in-out;">
            <Measuring :dataMap="deviceInfo" :curMeasuring="latestMeasure"></Measuring>
        </view>
        <!-- 失败提示 -->
        <!-- <up-notify ref="uNotifyRef" message=""></up-notify> -->
    </view>
</template>
<script setup lang="ts">
import { getDevice } from '@/common/api/task';
import { onLoad, onShow, onHide } from "@dcloudio/uni-app";
import { ref, reactive, onMounted, onBeforeUnmount, computed, nextTick, watch } from 'vue';


import Measuring from './Measuring.vue';
import device2 from '/static/products/blood-pressure02.png';
import device3 from '/static/products/blood-pressure01.png';

const images = {
    SPHY: device2,
    HYO2: device3
}
const emits = defineEmits(['startMeasure', 'refreshList'])

const props = defineProps({
    device: {
        type: Object,
        default: () => { },
    },
})

// 加载
const timer: any = ref(null)
const outoPoll = ref(false)
const deviceInfo: any = ref({})

// 获取设备编号
const deviceNo = computed(() => {
    const str = deviceInfo.value?.deviceNo
    if (!str) {
        return '-'
    }
    // 获取后四位字符
    return str.slice(-4)
})

const latestMeasure = computed(() => {
    if (!deviceInfo.value.latestMeasure) {
        return {}
    }

    return JSON.parse(deviceInfo.value.latestMeasure)
})

// const tipId = ref('')
// watch(() => latestMeasure.value, (later) => {

//     const measureTime = '' + later.measureTime
//     // 0 = 测量失败
//     // 提示id 不相等。不重复提示
//     // undefined = 
//     if (later.measureStatus === 0 && tipId.value !== measureTime) {
//         uNotifyRef.value.show({  
//             top: 0.1,  
//             type: 'error',  
//             message: '测量失败：' + later.measureMsg,  
//             duration: 1000 * 5,  
//             fontSize: 20,  
//             safeAreaInsetTop: true  
//         });  
//         tipId.value = measureTime
//     }
// })



const capacityPercent = computed(() => {
    const str = deviceInfo.value?.capacityPercent
    if (!str) {
        return '-'
    }
    return Math.round(str) + '%'
})

// 设备状态 0 关机 1在线 2休眠 3断网
const onlineArr = ['关机', '在线', '休眠', '断网']
const onlineState = computed(() => {
    const onlineState = deviceInfo.value?.onlineState || 0

    return onlineState
})

// 设备测量进行时状态
// 0=空闲；1=血氧测量中；2=体温测量中；3=血压测量中；20=测量完成；30=等待下次测量；;
const deviceMeasureStatus = computed(() => {
    if (deviceInfo.value.measureStatus === null) {
        return 0
    }
    return deviceInfo.value.measureStatus
})


// 显示按钮状态
// 0，不可操作，1、可操作，2、进行时 3、电量低
const showBtnStatus = computed(() => {


    // 进行时？是否要判断设备状态先
    if (deviceMeasureStatus.value === 1 || deviceMeasureStatus.value === 3) {
        return 2
    }

    // 设备不在线
    if (onlineState.value !== 1) {
        return 0
    }

    // 状态处于，空闲、测量完成、等待
    // 可以操作
    if (deviceMeasureStatus.value === 0 || deviceMeasureStatus.value === 20 || deviceMeasureStatus.value === 30) {
        return 1
    }

    return 0
})


// 显示测量中状态屏
const showMeasureBox = ref(false)
let showMeasureBoxTimer: any = null

watch(() => showBtnStatus.value, () => {
    clearTimeout(showMeasureBoxTimer)
    // 2,进行中。一直显示
    if (showBtnStatus.value == 2) {
        showMeasureBox.value = true
    }else {
        // 如果变了状态等8秒隐藏
        showMeasureBoxTimer = setTimeout(() => {
            showMeasureBox.value = false
        }, 1000 * 8);
    }
})


// 监听测量状态
let exceedTimer: any = null
// const uNotifyRef: any = ref({})

function refreshList() {
    emits('refreshList')
}
watch(() => deviceMeasureStatus.value, (val) => {
    // 状态为测量中
    if (val === 20) {
        // 测量完成
        clearTimeout(exceedTimer);
        exceedTimer = setTimeout(refreshList, 1000);
    }
}, { immediate: true })

const onClickMeasure = () => {
    emits('startMeasure')
}

const queryData = () => {
    if (!props?.device?.deviceId) {
        return
    }
    getDevice({ deviceId: props.device.deviceId }).then((res: any) => {
        // console.log(res)
        const data = res.data || {}
        // if (setId) {

            // const latestMeasure = JSON.parse(data.latestMeasure || '{}')

            // tipId.value = '' + latestMeasure.measureTime
        // }

        deviceInfo.value = data
    }).catch(() => {
        // 请求失败，设置为离线
        deviceInfo.value.onlineState = 0
    }).finally(() => {
        clearTimeout(timer.value)

        if (!outoPoll.value) {
            return
        }
        timer.value = setTimeout(() => {
            queryData()
        }, 1000 * 2.4);
    })
}


onMounted(() => {
    outoPoll.value = true
    queryData()
});
onBeforeUnmount(() => {
    clearTimeout(timer.value)
    outoPoll.value = false
})
</script>
<style scoped lang="scss">
.b-tn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 74rpx;
    background: #e99d42;
    border-radius: 12rpx;
    color: #fcfcfc;
    box-shadow: rgba(0, 0, 0, 0.15) 0px 5px 15px 0px;

    &.not-tn {
        background: #BEBEBE;
    }

    &.connect-tn {
        position: relative;
        background: #ffffff;
        color: #02BB00;

        //border: 2px solid #d3efdc;
    }

    &.connect-tn::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        border: 2px solid transparent;
        border-radius: 12rpx;
        animation: border-animation 3s infinite;
    }
}

.online {
    font-size: 28rpx;

    &::before {
        position: relative;
        top: 4rpx;
        margin-right: 8rpx;
        content: "";
        display: inline-block;
        width: 30rpx;
        height: 30rpx;
        border-radius: 50%;
        background: #999999;
    }

    &.online1 {
        &::before {
            background: #02BB00;
        }
    }

    &.online2 {
        &::before {
            background: #d1bf30;
        }
    }
}


/* 边框颜色渐变动画 */
@keyframes border-animation {
    0% {
        border-top-color: #d3efdc;
        border-right-color: transparent;
        border-bottom-color: transparent;
        border-left-color: transparent;
    }

    25% {
        border-top-color: #1ABA62;
        border-right-color: #d3efdc;
        border-bottom-color: transparent;
        border-left-color: transparent;
    }

    50% {
        border-top-color: #1ABA62;
        border-right-color: #1ABA62;
        border-bottom-color: #d3efdc;
        border-left-color: transparent;
    }

    75% {
        border-top-color: #1ABA62;
        border-right-color: #1ABA62;
        border-bottom-color: #1ABA62;
        border-left-color: #d3efdc;
    }

    100% {
        border-top-color: #1ABA62;
        border-right-color: #1ABA62;
        border-bottom-color: #1ABA62;
        border-left-color: #1ABA62;
    }
}
	/* 3 DOT LOADER */

	.dot-loader {
		height: 12rpx;
		width: 12rpx;
		border-radius: 50%;
		background-color: #f44336;
		position: relative;
		-webkit-animation: 1.2s grow ease-in-out infinite;
		animation: 1.2s grow ease-in-out infinite;
	}

	.dot-loader--2 {
		-webkit-animation: 1.2s grow ease-in-out infinite 0.15555s;
		animation: 1.2s grow ease-in-out infinite 0.15555s;
		margin: 0 10rpx;
	}

	.dot-loader--3 {
		-webkit-animation: 1.2s grow ease-in-out infinite 0.3s;
		animation: 1.2s grow ease-in-out infinite 0.3s;
	}
    @keyframes grow {

0%,
40%,
100% {
    -webkit-transform: scale(0);
    transform: scale(0);
}

40% {
    -webkit-transform: scale(1);
    transform: scale(1);
}
}
</style>