<template>
	<view class="p-[30rpx] pb-0 h-full flex flex-col bg-[#e3ecf2] overflow-hidden box-border">
		<view class="flex bg-[#1296db] h-[140rpx] rounded-[24rpx] text-white pl-[60rpx] py-[20rpx]" style="box-shadow: 0px 4rpx 20rpx rgba(42, 130, 228, 0.15);">
			<view @click="handleBluetooth" class="text-[44rpx] flex-1 flex flex-col justify-center">
				<text >进入蓝牙测量</text>
			</view>

			<view class=" mx-[20rpx] flex justify-center items-center">
				<image src="@/static/img/bluetooth.png" class="w-[80rpx] h-[80rpx] object-contain" mode="widthFix" />
			</view>
		</view>
		<view class="top-filter">
			<view v-for="item in tabsList" :key="item.id" :class="{ 'filter-box': 1, highlight: item.id == current }"
				@click="changeTab({ id: item.id })">
				<view class="up">
					<text class="t">{{ item.name }}
						<!-- <text v-if="item.badge.value">({{ item.badge.value || "0" }})</text> -->
					</text>
				</view>
			</view>
		</view>
		<z-paging
			ref="paging"
			v-model="dataList"
			@refresh="refresh"
			@query="queryList"
            :auto="false"
			:fixed="false"
			:auto-show-back-to-top="false"
		>
			<view class="flex-1 overflow-auto">
				<view v-for="item in dataList" :key="item.parentId" @click="handleDetail(item)"
					class="bg-white p-[40rpx] pl-[50rpx] rounded-[16rpx] flex mb-[30rpx]">
					<view class="flex flex-col text-[32rpx] flex-1 overflow-hidden">
						<view class="flex items-center">
							<text class="font-bold text-[34rpx] overflow-hidden min-w-[120rpx]">{{
								item.patientName
							}}</text>
							<text v-if="item.deviceNo" class="pl-[20rpx] truncate">设备编号：{{ item.deviceNo?.slice(-4)
								}}</text>
							<text v-else class="pl-[20rpx] truncate flex-1 overflow-hidden">{{
								item.itemName
							}}</text>
						</view>
						<text class="w-full h-[1px] bg-[#c7c7c7] mt-[20rpx] mb-[20rpx]"></text>
						<view class="color-[#999999]">
							<text class="pr-[16rpx]">{{ item.sexText }}</text>
							<text class="pr-[16rpx]">{{ item.age }}</text>
							<text class="pr-[16rpx]">预约时间：{{
								moment(item.appointmentTime).format("HH:mm") || "-"
							}}</text>
							<!-- <text class="color-[#c75045]">{{ item.medicalHistory }}</text> -->
						</view>
						<view class="pt-[20rpx]">使用
							<text class="color-[#01ba66]"><text :class="{ underline: !armName(item.arm) }">{{
								armName(item.arm) || "　"
							}}</text></text>
							佩戴设备 臂围：<text :class="{ underline: !item.armGirth }">{{
								item.armGirth || "　"
							}}</text>
							CM
						</view>
					</view>
					<view class="flex items-center pl-[24rpx]">
						<uni-icons custom-prefix="iconfont" type="icon-youcejiantou-color" size="26"
							color="#999"></uni-icons>
					</view>
				</view>
			</view>
            <template #empty>
                <up-empty mode="list" text="无测量患者" icon="/static/img/empty1.png" height="100" width="174"></up-empty>
            </template>
		</z-paging>
	</view>

    <!-- NFC绑定弹窗 -->
    <u-popup 
        :show="showNFCPopup" 
        mode="bottom" 
        round="20"
        :closeable="true"
        @close="closeNFCPopup"
        :safeAreaInsetBottom="true"
    >
        <view class="nfc-animation-box">
            <image 
                src="@/static/products/blood-pressure01.png" 
                class="nfc-device" 
                mode="aspectFit" 
            />
            <image
                src="@/static/products/iPhone.png"
                class="nfc-phone"
                mode="aspectFit"
            />
        </view>
        <view class="text-lg color-gray-600 mb-4 text-center">请将手机靠近设备NFC区域</view>
        <view class="text-sm color-gray-400 text-center">正在等待NFC识别...</view>
        <view class="h-[80rpx]"></view>
    </u-popup>
</template>

<script setup lang="ts">
import {
	ref,
	reactive,
	onMounted,
	onBeforeUnmount,
	computed,
	nextTick,
	watch,
} from "vue";
import {
	queryPatientCase,
	bindDevice,
    unbindDevice,
	updateMeasureStatus,
} from "@/common/api/task";
import moment from "moment";
import nfc from "@/common/nfc.js";
import { storeToRefs } from "pinia";
import { useNfcInfoStore } from "@/stores/nfcInfo";
import { onShow } from "@dcloudio/uni-app";

const current = ref(1);
const tabsList = ref([
	{ name: "待测量", id: 1, badge: { value: 0, bgColor: "#FF8D1A" } },
	{ name: "测量中", id: 2, badge: { value: 0, bgColor: "#5ac725" } },
	{
		name: "已完成",
		id: 3,
		badge: { value: 0, inverted: true, color: "#1c6ba4" },
	},
]);

const options = {
	arm: [
		{ value: 1, name: "左手臂" },
		{ value: 2, name: "右手臂" },
	],
};

const armName = (arm: any) => {
	if (!arm) {
		return "";
	}

	return options.arm.find((item: any) => item.value == arm)?.name;
};

// 改变 tab
function changeTab(item: any) {
	current.value = item.id;
	queryList();
}

interface PatientItem {
	parentId: number;
	patientName: string;
	appointmentTime: string;
	sexText?: string;
	deviceNo: string;
	itemName: string;
	age?: number;
	arm?: string;
	armGirth?: number;
	medicalHistory: string;
}

const dataList = ref<PatientItem[]>([]);
const paging: any = ref(null)

// 新增响应式变量
const showNFCPopup = ref(false);
let pendingPatient: any = null; // 保存等待绑定的患者信息

async function queryList(pageNo: any = 1) {
	const params = {
		condition: {
			startTime: moment().subtract(2, "days").format("YYYY-MM-DD 00:00:00"),
			endTime: moment().format("YYYY-MM-DD 23:59:59"),
			stage: current.value,
		},
		pageNum: pageNo,
		pageSize: 99,
	};

	queryPatientCase(params).then((res) => {
		// 处理分页完成
		paging.value?.complete(res.data.list || []);
		// 更新角标数量
		tabsList.value.forEach(tab => {
			const count = res.data.list?.filter((item: any) => item.stage === tab.id).length || 0
			tab.badge.value = count
		})
	});
}

// 点击进入患者测量详情
async function handleDetail(item: any) {
	if (item.executeStatus === 0) {
		if (item.deviceNo) {
			updateMeasureStatus({
				measureStatus: 10,
				patientId: item.patientId,
			}).then(() => {
				item.executeStatus = 10;
				// 传递参数，跳转
				const patient = encodeURIComponent(JSON.stringify(item));

				uni.navigateTo({
					url: "/pages/home/<USER>" + patient,
					animationType: "slide-in-right",
					animationDuration: 300,
				});
			});
		}else if (!item.deviceNo) {
			pendingPatient = item;
			showNFCPopup.value = true;
			return;
		}
		return;
	}

	const patient = encodeURIComponent(JSON.stringify(item));

	uni.navigateTo({
		url: "/pages/home/<USER>" + patient,
		animationType: "slide-in-right",
		animationDuration: 300,
	});
}
const showModel = ref(false)

// 新增绑定方法
function bindDeviceAndNavigate(item: any, deviceNo: string) {
	bindDevice({ patientId: item.patientId, deviceNo: deviceNo })
		.then((res: any) => {
			if (res.success) {
				item.deviceNo = deviceNo;
				updateMeasureStatus({
					measureStatus: 10,
					patientId: item.patientId,
				}).then(() => {
					item.executeStatus = 10;
					const patient = encodeURIComponent(JSON.stringify(item));
					uni.navigateTo({
						url: "/pages/home/<USER>" + patient,
						animationType: "slide-in-right",
						animationDuration: 300,
					});
				});
			} else if (res.code === 421) {
                showModel.value = true
                const { patientId, deviceNo, patientName } = res.data
                uni.showModal({
                    title: '提示',
                    content: `设备已被 ${patientName} 绑定，是否强制解绑？`,
                    success: (res: any) => {
                        if (res.confirm) {
                            unbindDevice({ patientId, deviceNo }).then(() => {
                                bindDeviceAndNavigate(item, deviceNo)
                            })
                        }
                    },
                    complete: () => {
                        showModel.value = false
                    }
                })
            }else {
				uni.showToast({ title: res.msg, icon: "none" });
			}
		})
		.catch(() => {
			uni.showToast({ title: "绑定失败！", icon: "error" });
		});
}

// 新增关闭弹窗方法
function closeNFCPopup() {
	showNFCPopup.value = false;
	pendingPatient = null;
}

const handleBluetooth = () => {
	uni.navigateTo({
		url: "/pages/bluetooth/index",
		animationType: "slide-in-right",
		animationDuration: 300,
	});
};

// 下拉刷新
const refresh = () => {
	queryList(1)
}

onMounted(() => {
	// #ifdef APP-PLUS
	nfc.listenNFCStatus();
	nfc.readData();
	// #endif
    console.log('onMounted')
});
onBeforeUnmount(() => {
    console.log('onBeforeUnmount')
	// #ifdef APP-PLUS
	nfc.stopRead();
	// #endif
});
onShow(() => {
	queryList();

    const pages = getCurrentPages()
    console.log(pages)
});

watch(
	() => useNfcInfoStore().getNfcId,
	(newValue) => {
        if (newValue === null) {
            return
        }

        // 要清除之前的 showModal
        if (showModel.value) {
            return
        }
		
		if (showNFCPopup.value && newValue) {
			// 自动关闭弹窗并继续绑定流程
			showNFCPopup.value = false;
			if (pendingPatient) {
				bindDeviceAndNavigate(pendingPatient, newValue);
				pendingPatient = null;
			}
		}else {
            // 获取当前路由
            const pages = getCurrentPages()
            // 获取当前页面对象
            const currentPage: any = pages[pages.length - 1]
            // 获取完整路径（包含参数）
            const fullPath = currentPage?.$page?.fullPath
            console.log('当前完整路径：', fullPath)
            if (fullPath === '/pages/home/<USER>') {
                // 提示先选择待测量患者
                uni.showModal({
                    title: '提示',
                    content: '请先选择待测量患者',
                })
            }
        }
	}
);
</script>

<style scoped lang="scss">
page {
	background: #e3ecf2;
	height: 100%;
}

.top-filter {
	position: relative;
	width: 100%;
	display: flex;
	flex-direction: row;
	align-items: center;
	justify-content: space-around;
	background: #fff;
	padding: 18rpx 20rpx;
	border-radius: 24rpx;
	flex: 0;
	margin: 24rpx 0;
	box-sizing: border-box;
	color: #757575;
	// box-shadow: 0px 3px 4px 1px #eee;

	.filter-box {
		flex: 1;
		position: relative;

		width: 100%;
		display: flex;
		flex-direction: column;
		justify-content: space-evenly;

		align-items: center;
		overflow: visible;

		&.highlight {
			.up {
				background: #1296db;

				.t {
					color: white;
				}
			}
		}

		.up {
			padding: 16rpx 40rpx;
			position: relative;
			border-radius: 40rpx;

			.t {
				font-size: 34rpx;
			}
		}

		.down {
			position: relative;

			.t {
				font-size: 40rpx;
			}
		}
	}
}

.nfc-animation-box {
	position: relative;
	width: 600rpx;
	height: 300rpx;
	margin: 40rpx auto;
	display: flex;
	align-items: center;
	justify-content: center;
}

.nfc-device {
	width: 240rpx;
	height: 240rpx;
	flex-shrink: 0;
	position: relative;
    left: -40rpx;
	z-index: 1;
}

.nfc-phone {
	width: 200rpx;
	height: 200rpx;
	position: absolute;
	right: 0;
    top: 34px;
	animation: phone-approach 3s ease-in-out infinite;
}

@keyframes phone-approach {
	0% {
		transform: translateX(30%);
		opacity: 0;
	}
	10% {
		transform: translateX(29%);
		opacity: 1;
	}
	70% {
		transform: translateX(-60%);
		opacity: 1;
	}
	100% {
		transform: translateX(-60%);
		opacity: 0;
	}
}

:deep(.u-popup) {
	padding: 40rpx 30rpx;
}
</style>
