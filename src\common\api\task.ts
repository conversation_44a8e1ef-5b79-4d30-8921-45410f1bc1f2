import request from '@/common/request'

/**
 * 实时分页查询任务
 * @param data 请求体参数
 */
export const queryPatientCase = (data : any) => {
	return request({
		method: 'POST',
		url: '/abpm/query/appPageQueryPatientCase',
		body: data,
		hideLoading: false
	})
}

/**
 * 获取设备信息
 * @param data 请求体参数
 */
export const getDevice = (data : any) => {
	return request({
		method: 'POST',
		url: '/device/device/getDeviceById',
		body: data,
		hideLoading: true,
        headers: {
            'content-type': 'application/x-www-form-urlencoded',
        },
	})
}

/**
 * 获取当前绑定的设备及测量计划
 * @param data 请求体参数
 */
export const getPatientBindingDevice = (data : any) => {
	return request({
		method: 'POST',
		url: '/abpm/measure/getPatientBindingDevice',
		body: data,
		hideLoading: false,
        headers: {
            'content-type': 'application/x-www-form-urlencoded',
        },
	})
}


/**
 * 启动血压单次测量
 * @param data 请求体参数
 */
export const startBpPointMeasure = (data : any) => {
	return request({
		method: 'POST',
		url: '/abpm/measure/startBpPointMeasure',
		body: data,
		hideLoading: false,
	})
}

/**
 * 获取测量记录
 * @param data 
 * @returns 
 */
export const queryPatientMeasureRecord = (params : any, hideLoading: boolean = true) => {
	return request({
		method: 'POST',
		url: '/abpm/measure/queryPatientMeasureRecord',
		body: params,
		hideLoading,
	})
}


/**
 * 更新病例的袖带手臂等参数信息
 * @param data 
 * @returns 
 */
export const updateMeasureParams = (data : any) => {
	return request({
		method: 'POST',
		url: '/abpm/measure/updateMeasureParams',
		body: data,
		hideLoading: false,
	})
}

/**
 * 更新病例的袖带手臂信息
 * @param data 
 * @returns 
 */
export const updateArm = (data : any) => {
	return request({
		method: 'POST',
		url: '/abpm/measure/updateArm',
		body: data,
		hideLoading: false,
	})
}
/**
 * 更新病例的测量状态
 * @param data 
 * @returns 
 */
export const updateMeasureStatus = (data : any) => {
	return request({
		method: 'POST',
		url: '/abpm/measure/updateMeasureStatus',
		body: data,
		hideLoading: false,
	})
}

/**
 * 设置计划并启动测量
 * @param data 
 * @returns 
 */
export const startHomeMeasure = (data : any) => {
	return request({
		method: 'POST',
		url: '/abpm/measure/startHomeMeasure',
		body: data,
		hideLoading: false,
	})
}

/**
 * 停止计划
 * @param data 
 * @returns 
 */
export const stopHomeMeasure = (data : any) => {
	return request({
		method: 'POST',
		url: '/abpm/measure/stopHomeMeasure',
		body: data,
		hideLoading: false,
        headers: {
            'content-type': 'application/x-www-form-urlencoded',
        },
	})
}


/**
 * 解绑设备
 * @param data 
 * @returns 
 */
export const bindDevice = (data : any) => {
	return request({
		method: 'POST',
		url: '/abpm/measure/bindDevice',
		body: data,
		hideLoading: false,
	})
}


/**
 * 解绑设备
 * @param data 
 * @returns 
 */
export const unbindDevice = (data : any) => {
	return request({
		method: 'POST',
		url: '/abpm/measure/unbindDevice',
		body: data,
		hideLoading: false,
	})
}

/**
 * 编辑测量记录
 * @param data 
 * @returns 
 */
export const editMeasureRecord = (data : any) => {
	return request({
		method: 'POST',
		url: '/abpm/measure/editMeasureRecord',
		body: data,
		hideLoading: false,
	})
}