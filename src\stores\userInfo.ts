/*
 * @Description:
 * @Author: shuliang
 * @Date: 2022-06-27 15:57:20
 * @LastEditTime: 2022-06-27 16:17:59
 * @LastEditors: shuliang
 */
import { defineStore } from 'pinia'
import type { UserInfosStates, ILoginResp } from '@/models/login'
import { queryAllDictAndItem } from '@/common/api/login'

/**
 * 用户信息
 * @methods setUserInfos 设置用户信息
 */
export const useUserInfo = defineStore('userInfo', {
	state: () : UserInfosStates => ({
		userInfos: uni.getStorageSync('userInfo') || {
            areaId: "",
			userId: "",
			token: ".",
			userName: "",
		},
		token: uni.getStorageSync('token'),
        dictCodeInfo: uni.getStorageSync('dictCodeInfo') || {},
	}),
    getters: {
        userId: (state) => state.userInfos?.userId,  
    },
	actions: {
		async setUserInfos(userInfos : ILoginResp) {
			// 存储用户信息到浏览器缓存
			uni.setStorageSync('userInfo', userInfos)
			this.userInfos = userInfos
		},
		async setToken(token : string) {
			// 存储用户信息到浏览器缓存
			uni.setStorageSync('token', token)
			this.token = token
		},

		async removeUserInfos() {
			// 存储用户信息到浏览器缓存
			uni.removeStorageSync('userInfo')
			this.userInfos = {
				personId: "",
                userId: "",
                token: ".",
                personInfo: {},
			}
		},
		async removeToken() {
			// 存储用户信息到浏览器缓存
			uni.removeStorageSync('token')
			this.token = ""
		},

        /**
         * 查询所有字典和字典项
         */
        async queryAllDictList() {
            const res = await queryAllDictAndItem()
            res?.data.forEach((dict: any) => {
                this.dictCodeInfo[dict.dictCode] = dict.items.filter((item: any) => item.state == 1);
            })

            // 对象形式
            // res?.data.forEach((dict: any) => {
            //     const dictList: any = {}
            //     dict.items.forEach((item: any) => {
            //         //：1=启用
            //         if (item.state == 1) {
            //             dictList[item.itemValue] = item.itemName;
            //         }
            //     });
            //     this.dictCodeInfo[dict.dictCode] = dictList;
            // })
            // 缓存
            uni.setStorageSync('dictCodeInfo', this.dictCodeInfo)
        }
	},
})