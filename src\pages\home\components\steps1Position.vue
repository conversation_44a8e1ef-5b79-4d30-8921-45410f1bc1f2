<template>
    <view class="mb-[20rpx]">
        <view style="background-color: #E3ECF2;position: sticky;z-index: 1;top: -1px;">
            <view class="text-[34rpx] py-[30rpx] flex justify-between items-center">
                <text style="text-shadow: 3px 3px 3px #b9b9b9;">诊室测量-体位压差分析</text>

                <view v-if="props.steps === 20" @click="onClickSure" :class="{'bg-[#BEBEBE]': dataList.length == 0 }" class="w-[220rpx] py-[16rpx] text-center rounded-[40rpx] bg-[#237FC2] color-white">完成分析</view>
                <uni-icons v-if="props.steps > 20" custom-prefix="iconfont" type="icon-dagou"size="28" color="#1ABA62"></uni-icons>
            </view>
        </view>
        <view v-if="props.steps > 20">
            <view class="bg-white px-[20rpx] py-[30rpx] rounded-[16rpx] flex justify-between items-center">
                <view class="color-[#0d0d0d] text-[28rpx]">体位压差测量结果</view>
                <view class="flex">
                    <!-- 设置计划步骤，并且没有计划还可以返回 -->
                    <view  v-if="props.steps === 30 && props.device.planJson == null" @click="onReset" class="flex justify-between items-center pr-[20rpx]">
                        <uni-icons custom-prefix="iconfont" type="icon-reset"size="20" color="#1296DB"></uni-icons><text class="pl-[8rpx] text-[28rpx] color-[#999] font-600">重测</text>
                    </view>
                    <view @click="onShowDetail" class="flex justify-between items-center pl-[20rpx]">
                        <uni-icons custom-prefix="iconfont" type="icon-down"size="20" color="#1296DB"></uni-icons><text class="pl-[12rpx] text-[28rpx] color-[#999] font-600">详情</text>
                    </view>
                </view>
            </view>
        </view>
        <view v-else>
            <!-- <view class="text-[36rpx] pt-[20rpx] pb-[20rpx]" style="text-shadow: 3px 3px 3px #b9b9b9;"></view> -->
            <!-- <view class="h-[100rpx] bg-white leading-[100rpx] text-[36rpx] pl-[20rpx]">体位压差测量</view> -->
            <view v-for="arrItem in dataList" class=" bg-white mb-[20rpx] text-[32rpx]">
                <view v-for="item in arrItem" class="pb-[40rpx]">
                    <view v-if="item.customType != -1 " class="h-[1px] w-full bg-[#DDDDDD]"></view>
                    <view class="flex justify-between items-center px-[30rpx] py-[20rpx]">
                        <text>{{ moment(item.measureTime).format('YYYY-MM-DD HH:mm') }}</text>
                        <text class="text-[26rpx]" :style="{color: item.customType }">{{ item.customDiffDate ? '间隔：'+item.customDiffDate : item.customDiffDate }}</text>
                    </view>
                    <view class="h-[1px] w-full bg-[#DDDDDD] mb-[30rpx]"></view>
                    <view class="flex px-[30rpx]">
                        <view class="flex flex-col items-center flex-1">
                            <text class="text-[56rpx] pt-[20rpx] pb-[20rpx]">{{ item.sbp || '-' }}</text>
                            <text class="text-[28rpx] color-[#333333]">收缩压</text>
                        </view>
                        <view class="flex flex-col items-center flex-1">
                            <text class="text-[56rpx] pt-[20rpx] pb-[20rpx]">{{ item.dbp || '-' }}</text>
                            <text class="text-[28rpx] color-[#333333]">舒张压</text>
                        </view>
                        <view class="flex flex-col items-center flex-1">
                            <text class="text-[56rpx] pt-[20rpx] pb-[20rpx]">{{ item.pulse || '-' }}</text>
                            <text class="text-[28rpx] color-[#333333]">脉搏</text>
                        </view>
                        <view class="flex flex-col items-center w-[160rpx] rounded-[20rpx] bg-[#eee]" @click="onClickItem(item)">
                            <view class=" flex-1 flex items-center text-center px-[10rpx]">
                                <!-- <text v-if="item.customType == -1" class="text-[28rpx]">参照项</text> -->
                                <uni-icons v-if="item.customType == -1" custom-prefix="iconfont" type="icon-S_HOTERU" size="38"></uni-icons>
                                <text v-else class="text-[28rpx]">{{ types[item.customType] || '-' }}</text>
                            </view>
                        </view>
                    </view>
                </view>

            </view>
            <view v-if="!dataList.length" class="p-[30rpx] pt-[60rpx] pb-[40rpx] bg-white text-[32rpx]">
                <up-empty mode="list" text="无体位结果" icon="/static/img/empty1.png" height="83" width="145"></up-empty>
            </view>
            <popup v-model="editObject.visible" title="修改姿态" paddingContent="0">
                <view class="px-[20rpx] pt-[20rpx] min-h-[300rpx]">
                    <view class="form-item">
                        <view class="form-container flex justify-evenly">
                            <view v-for="item in postureOptions" @click="onClickPosture(item.itemValue)" :class="[ editObject.formData.posture == item.itemValue ? 'bg-[#9de4bd]!' : '']" class="flex flex-col items-center h-[160rpx] w-[160rpx] rounded-[20rpx] bg-[#eee]">
                                <view class=" flex-1 flex items-center text-center px-[10rpx]">
                                    <uni-icons custom-prefix="iconfont" :type="item.remark" size="38"></uni-icons>
                                </view>
                            </view>
                        </view>
                    </view>
                </view>
                <view slot="footer" class="flex justify-evenly mb-[20rpx]">
                    <view class="xx-button" @click="onClickEdit">确定</view>
                </view>
            </popup>
        </view>

        <!-- 添加详情弹窗 -->
        <up-popup :show="showRecommend" mode="center" :round="10" @close="showRecommend = false">
            <view class="detail-popup">
                <view class="detail-header flex justify-between items-center px-[30rpx] h-[120rpx] text-center text-[40rpx] bg-[#EFEFEF]">
                    <text class="text-[32rpx] font-bold">体位压差详情</text>
                    <uni-icons @click="showRecommend = false" type="closeempty" size="20"></uni-icons>
                </view>
                <view class="detail-content px-[20rpx] pb-[30rpx]">
                    <view v-for="arrItem in dataList" class="bg-white mb-[20rpx] text-[32rpx]">
                        <view v-for="item in arrItem" class="pb-[40rpx]">
                            <view v-if="item.customType != -1 " class="h-[1px] w-full bg-[#DDDDDD]"></view>
                            <view class="flex justify-between items-center py-[20rpx]">
                                <text>{{ moment(item.measureTime).format('YYYY-MM-DD HH:mm') }}</text>
                                <text class="text-[26rpx]" :style="{color: item.customType }">{{ item.customDiffDate ? '间隔：'+item.customDiffDate : item.customDiffDate }}</text>
                            </view>
                            <view class="h-[1px] w-full bg-[#DDDDDD] mb-[30rpx]"></view>
                            <view class="flex">
                                <view class="flex flex-col items-center flex-1">
                                    <text class="text-[56rpx] pt-[20rpx] pb-[20rpx]">{{ item.sbp || '-' }}</text>
                                    <text class="text-[28rpx] color-[#333333]">收缩压</text>
                                </view>
                                <view class="flex flex-col items-center flex-1">
                                    <text class="text-[56rpx] pt-[20rpx] pb-[20rpx]">{{ item.dbp || '-' }}</text>
                                    <text class="text-[28rpx] color-[#333333]">舒张压</text>
                                </view>
                                <view class="flex flex-col items-center flex-1">
                                    <text class="text-[56rpx] pt-[20rpx] pb-[20rpx]">{{ item.pulse || '-' }}</text>
                                    <text class="text-[28rpx] color-[#333333]">脉搏</text>
                                </view>
                                <view class="flex flex-col items-center w-[160rpx] rounded-[20rpx] bg-[#eee]">
                                    <view class="flex-1 flex items-center text-center px-[10rpx]">
                                        <uni-icons v-if="item.customType == -1" custom-prefix="iconfont" type="icon-S_HOTERU" size="38"></uni-icons>
                                        <text v-else class="text-[28rpx]">{{ types[item.customType] || '-' }}</text>
                                    </view>
                                </view>
                            </view>
                        </view>
                    </view>
                </view>
            </view>
        </up-popup>

    </view>
</template>
<script setup lang="ts">
import { ref, reactive, onMounted, onBeforeUnmount, computed, nextTick } from 'vue';
import popup from '@/components/popup/popup.vue';
import { queryPatientMeasureRecord, updateMeasureStatus, editMeasureRecord } from '@/common/api/task';
import { deepClone } from '@/uni_modules/uview-plus';
import moment from 'moment';
import { useUserInfo } from '@/stores/userInfo';
const storeUserInfo = useUserInfo();

const props = defineProps({
    patient: {
        type: Object,
        default: () => {},
    },
    device: {
        type: Object,
        default: () => {},
    },
    steps: {
        type: Number,
        default: 0
    }
})

const dataList: any = ref([])
const types = ['正常', '初始形直立性低血压', '直立性低血压', '延迟形直立性低血压', '延迟血压恢复型直立性低血压', '体位高血压']


if (!storeUserInfo.dictCodeInfo.lieDown.length || !storeUserInfo.dictCodeInfo.stand.length) {
    uni.showToast({
        title: '请先配置体位压差字典 lieDown, stand',
        icon: 'none'
    })
}

// 2 躺，1 站
const postureOptions = [...storeUserInfo.dictCodeInfo.lieDown, ...storeUserInfo.dictCodeInfo.stand]

const lieDownValue = storeUserInfo.dictCodeInfo.lieDown[0]?.itemValue || 2
const standValue = storeUserInfo.dictCodeInfo.stand[0]?.itemValue || 1

// 获取测量记录
function queryList() {

    
    const params = {
        bpMeasureType: 20, // 体位
        patientId: props.patient.patientId
    }
    queryPatientMeasureRecord(params).then(res => {
        
        const data = (res.data || []).reverse().map((item: any) => {
            if (item.posture != lieDownValue) {
                item.posture = standValue
            }
            return item
        })

        function splitByPosture(arr: any, targetPosture = 2) {
            const result = []; // 存储最终结果的二维数组
            let currentGroup = []; // 当前分组
            for (const item of arr) {
                if (item.posture == targetPosture && currentGroup.length > 0) {
                // 如果当前项的 Posture  是目标值，并且当前分组不为空
                result.push(currentGroup); // 将当前分组加入结果
                currentGroup = []; // 重置当前分组
                }
                currentGroup.push(item); // 将当前项加入当前分组
            }

            // 最后将剩余的分组加入结果
            if (currentGroup.length > 0) {
                result.push(currentGroup);
            }


            result.forEach((arrItem: any) => {
                if (arrItem.length) {
                    // 第一个做参照值
                    const beforeItem = arrItem[0]
                    beforeItem.customType = -1
                    beforeItem.customName = '参照数据'

                    for (let index = 1; index < arrItem.length; index++) {
                        const activeItem = arrItem[index];
                        

                        const timeA = moment(beforeItem.measureTime);
                        const timeB = moment(activeItem.measureTime);
                        const customDiffDate = Math.abs(timeA.diff(timeB, 'second'));


                        const diffSbp = activeItem.sbp - beforeItem.sbp;
                        const diffDbp = activeItem.dbp - beforeItem.dbp;

                        // 自立测量
                        if (customDiffDate < 30 && (diffSbp <= -40 || diffDbp <= -20)) {
                            activeItem.customType = 1
                        }else if (diffSbp <= -20 || diffDbp <= -10) {
                            activeItem.customType = customDiffDate <= 180 ? 2 : 3
                        }else if (diffSbp > 20) {
                            activeItem.customType = 5
                        }else {
                            activeItem.customType = 0
                        }
                        activeItem.customDiffDate = customDiffDate < 60 ? customDiffDate+' s' : Math.round(customDiffDate / 60)+' min'
                        activeItem.customDiffSbp = diffSbp
                        activeItem.customDiffDbp = diffDbp

                        // TODO 延迟血压恢复型直立性低血压 未处理 3分钟时即恢复正常
                    }
                }
            })

            return result
        }
        dataList.value = splitByPosture(data, lieDownValue)
    })
}

// 推荐结果
const showRecommend = ref(false)

// 重测压差
const onReset = () => {
    uni.showModal({
        title: '提示',
        content: '是否重新测量体位压差？',
        success: (res) => {
            if (res.confirm) {
                updateMeasureStatus({ measureStatus: 20, patientId: props.patient.patientId }).then(() => {
                    props.patient.executeStatus = 20
                })
            }
        }
    })
}

const onClickSure = () => {
    uni.showModal({
        title: '提示',
        content: '是否结束体位压差测量？',
        success: (res) => {
            if (res.confirm) {
                updateMeasureStatus({ measureStatus: 30, patientId: props.patient.patientId }).then(() => {
                    props.patient.executeStatus = 30
                })
            }
        }
    })
}

const editObject = reactive({
    visible: false,
    rules: {},
    formData: {
        posture: 1
    }
})

const onClickItem = (item: any) => {
    if (dataList.value[0]) {
        if (dataList.value[0][0]?.recordId === item.recordId) {
            uni.showToast({
                icon: 'none',
                title: '第一条无法做修改',
            })
            return
        }
    }

    editObject.formData = deepClone(item) 

    editObject.visible = true
}
const onClickPosture = (value: number | string) => {
    editObject.formData.posture = Number(value)
}

// 点击编辑保存
const onClickEdit = () => {
    // 请求接口
    const params: any = Object.assign({}, editObject.formData)
    editMeasureRecord(params).then((res: any) => {
        if (res.success) {
            editObject.visible = false
            uni.showToast({
                icon: 'success',
                title: '修改成功！',
            })

            queryList()
        }
    })

}

// 显示详情
const onShowDetail = () => {
    showRecommend.value = true
    queryList()
}

onMounted(() => {
    queryList()
})

defineExpose({
    queryList,
    onClickSure
})
</script>
<style lang="scss" scoped>
.form-item {
    padding-bottom: 30rpx;
    .form-label {
        font-size: 32rpx;
        padding-bottom: 36rpx;
    }
    .form-container {
        position: relative;
        .uni-input {
            line-height: 68rpx;
            height: 68rpx;
            border-radius: 32rpx;
            border: 1px solid rgba(230, 230, 230, 1);
            text-align: center;
        }
        .i-more {
            position: absolute;
            right: 0;
            top: 0;
            height: 100%;
            width: 100rpx;
            text-align: center;
            font-size: 46rpx;
            line-height: 70rpx;
            z-index: 2;
        }

    }
}
.xx-button {
    font-size: 30rpx;
    height: 78rpx;
    width: 60%;
    line-height: 78rpx;
    text-align: center;
    background: #0D5BAB;
    padding: 0 22px;
    color: #fff;
    border: 1px solid #DCEDFF;
    border-radius: 32rpx;
}

.detail-popup {
    width: 700rpx;
    max-height: 80%;
    overflow-y: auto;
    
    .detail-header {
        border-bottom: 1px solid #eee;
    }
    
    .detail-content {
        padding: 20rpx;
    }
}
</style>