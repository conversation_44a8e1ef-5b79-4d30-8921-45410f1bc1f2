import { useNfcInfoStore } from '@/stores/nfcInfo'

// 包路径
const package_NdefRecord = 'android.nfc.NdefRecord';
const package_NdefMessage = 'android.nfc.NdefMessage';
const package_TECH_DISCOVERED = 'android.nfc.action.TECH_DISCOVERED';
const package_Intent = 'android.content.Intent';
const package_Activity = 'android.app.Activity';
const package_PendingIntent = 'android.app.PendingIntent';
const package_IntentFilter = 'android.content.IntentFilter';
const package_NfcAdapter = 'android.nfc.NfcAdapter';
const package_Ndef = 'android.nfc.tech.Ndef';
const package_NdefFormatable = 'android.nfc.tech.NdefFormatable';
const package_Parcelable = 'android.os.Parcelable';
const package_String = 'java.lang.String';
let NfcAdapter;
let NdefRecord;
let NdefMessage;
let readyWriteData = false; //开启写
let readyRead = true; //开启读
let noNFC = false;
let log = '';
let timer = null;
let techListsArray = [
    ['android.nfc.tech.IsoDep'],
    ['android.nfc.tech.NfcA'],
    ['android.nfc.tech.NfcB'],
    ['android.nfc.tech.NfcF'],
    ['android.nfc.tech.Nfcf'],
    ['android.nfc.tech.NfcV'],
    ['android.nfc.tech.NdefFormatable'],
    ['android.nfc.tech.MifareClassi'],
    ['android.nfc.tech.MifareUltralight']
];
export default {
    log: function(_) {
        return log
    },
    listenNFCStatus: function () {
        // console.log("---------监听NFC状态--------------")
        let that = this;
        try {
            let main = plus.android.runtimeMainActivity();
            let Intent = plus.android.importClass('android.content.Intent');
            let Activity = plus.android.importClass('android.app.Activity');
            let PendingIntent = plus.android.importClass('android.app.PendingIntent');
            let IntentFilter = plus.android.importClass('android.content.IntentFilter');
            NfcAdapter = plus.android.importClass('android.nfc.NfcAdapter');
            let nfcAdapter = NfcAdapter.getDefaultAdapter(main);

            if (nfcAdapter == null) {
                // uni.showToast({
                //     title: '设备不支持NFC！',
                //     icon: 'none'
                // })
                noNFC = true; 

                useNfcInfoStore().setNfcAvail(false)
                return;
            }
            if (!nfcAdapter.isEnabled()) {
                // uni.showToast({
                //     title: '请在系统设置中先启用NFC功能！',
                //     icon: 'none'
                // });
                useNfcInfoStore().setNfcAvail(false)
                noNFC = true;
                return;
            } else {
                noNFC = false;
            }
            let intent = new Intent(main, main.getClass());
            intent.addFlags(Intent.FLAG_ACTIVITY_SINGLE_TOP);
            let pendingIntent = PendingIntent.getActivity(main, 0, intent, 0);
            let ndef = new IntentFilter("android.nfc.action.TECH_DISCOVERED");
            ndef.addDataType("*/*");
            let intentFiltersArray = [ndef]; 
            useNfcInfoStore().setNfcAvail(true)
            console.log('NFC INIT')
            plus.globalEvent.addEventListener('newintent', function () {
                // console.log('newintent running');
                // 轮询调用 NFC
                    that.nfcRuning() 
            });
            plus.globalEvent.addEventListener('pause', function (e) {
                // console.log('pause running');
                if (nfcAdapter) {
                    //关闭前台调度系统
                    //恢复默认状态
                    nfcAdapter.disableForegroundDispatch(main);
                }
            });
            plus.globalEvent.addEventListener('resume', function (e) {
                // console.log('resume running');
                if (nfcAdapter) {
                    //开启前台调度系统
                    // 优于所有其他NFC
                    nfcAdapter.enableForegroundDispatch(main, pendingIntent, intentFiltersArray,
                        techListsArray);
                }
            });
            nfcAdapter.enableForegroundDispatch(main, pendingIntent, intentFiltersArray, techListsArray);
        } catch (e) {
            console.error(e);
            // uni.showToast({
            //     title: '报错！',
            //     icon: 'none'
            // });
        }
    },
    nfcRuning: function () { //
        // console.log("--------------NFC 运行---------------")
        NdefRecord = plus.android.importClass("android.nfc.NdefRecord");
        NdefMessage = plus.android.importClass("android.nfc.NdefMessage");
        let main = plus.android.runtimeMainActivity();
        let intent = main.getIntent();
        let that = this;
        // console.log("action type:" + intent.getAction());
        // console.log(package_TECH_DISCOVERED == intent.getAction());
        if (package_TECH_DISCOVERED == intent.getAction()) {
            // console.log(readyRead)
            if (readyRead) {
                // console.log("----------我在读1-------------")
                useNfcInfoStore().setNfcId(null)
                clearTimeout(timer)
                timer = setTimeout(() => {
                    that.read(intent);
                }, 600)
            }
        }
    },
    read(intent) { // 读代码
        // console.log("----------我在读read-------------")
        // toast('请勿移开标签正在读取数据');
        try {
            let rawmsgs = intent.getParcelableArrayExtra("android.nfc.extra.NDEF_MESSAGES");
            if(rawmsgs != null && rawmsgs.length > 0) {

                let records = rawmsgs[0].getRecords();
                let target
                let readResult
                records.forEach(element => {
					// 获取记录的 type
					// let typeBytes = plus.android.invoke(element, "getType");
					// let typeStr = plus.android.newObject("java.lang.String", typeBytes, "UTF-8");
					// console.log('Record Type:', typeStr);
					
					// payload data 值
                    let result = element.getPayload();
                    let str = plus.android.newObject("java.lang.String", result);
                    const parts = String(str).split(',');
                    if (parts.length === 2) {
                      const [a, b] = parts;
                      readResult = a.length === 12 ? a : b.length === 12 ? b : readResult;
                    }
                    // console.log(element.__UUID__ + '  数据：', arr);
                });
                // toast('NFC 98CDAC4DD598,ET05U1710061 ' + data);
                    console.log('NFC 数据：', readResult);
                useNfcInfoStore().setNfcId(readResult)
      
            }else{
                // toast('没有读取到数据');
            }
        } catch (error) {
        //   toast('没有读取到数据');
            console.log(error)
        }
        readyRead = true

          
    },
    byteArrayToHexString: function (inarray) { // 将字节数组转换为字符串
        let i, j, inn;
        let hex = ["0", "1", "2", "3", "4", "5", "6", "7", "8", "9", "A", "B", "C", "D", "E", "F"];
        let out = "";
        for (j = 0; j < inarray.length; ++j) {
            inn = inarray[j] & 0xff;
            i = (inn >>> 4) & 0x0f;
            out += hex[i];
            i = inn & 0x0f;
            out += hex[i];
        }
        return out;
    }, 
    readData: function () { // 更改读状态
        if (noNFC) {
            // toast('请检查设备是否支持并开启 NFC 功能！');

            return;
        }
        // 轮询条件
        readyRead = true;
        // toast('请将NFC标签靠近！');
    },
    stopRead: function () {
        readyRead = false;
    },
    // close: function(){
    // }, 
}
function toast(content) {
    uni.showToast({
        title: content,
        icon: 'none',
        duration: 2500
    })
}