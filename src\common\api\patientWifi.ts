import request from '@/common/request'
/**
 * 分页查询病例wifi(app)
 * @param 主类型 
 */
export const getAppQueryPatientWifi = (body : any) => {
	return request({
		method: 'POST',
		url: '/abpm/query/appQueryPatientWifi',
		body: body,
		hideLoading: false,
		custom: {
			ignoreRespInterceptors: false, //忽略本次请求的响应拦截
			isMockApi: false //需要返回本地mock数据时请配置为true
		}
	})
}
/**
 * 新增病人家庭wifi
 * @param 主类型 
 */
export const createPatientWifi = (body : any) => {
	return request({
		method: 'POST',
		url: '/abpm/measure/createPatientWifi',
		body: body,
		hideLoading: false,
		custom: {
			ignoreRespInterceptors: false, //忽略本次请求的响应拦截
			isMockApi: false //需要返回本地mock数据时请配置为true
		}
	})
}

/**
 * 修改病人家庭wifi
 * @param 主类型 
 */
export const editPatientWifi = (body : any) => {
	return request({
		method: 'POST',
		url: '/abpm/measure/editPatientWifi',
		body: body,
		hideLoading: false,
		custom: {
			ignoreRespInterceptors: false, //忽略本次请求的响应拦截
			isMockApi: false //需要返回本地mock数据时请配置为true
		}
	})
}
