<template>
    <view class="container-device" :class="{ 'no-online': (onlineState == 0 || onlineState == 3) && !props.dataMap.batteryState}">
        <view class="head flex-row-between">
            <view class="title">{{ props.dataMap.groupName }}</view>
            <view class="status xx-flex-center" v-if="onlineState != 0">
                <text class="dot"></text>
                <view class="iconfont icon-wifi" style="padding-right: 16rpx;"></view>
                <view class="iconfont icon-xiumian blink" style="padding-right: 16rpx;" v-if="props.dataMap.onlineState === 2"></view>
                <ElectricQuantity class="iconfont" :ElectricQuantity="props.dataMap.capacityPercent"></ElectricQuantity>
                <view class="hover-more">
                    <up-icon name="more-dot-fill" color="#ffffff" size="32rpx" style="margin-right: 10rpx;">
                    </up-icon>
                    <view class="hover-box" v-if="showMoreBtn">
                        <view class="box-item" @click="changeDevice">解绑设备</view>
                    </view>
                </view>
            </view>
            <view v-else class="hover-more">
                <up-icon name="more-dot-fill" size="32rpx" style="margin-right: 10rpx;">
                </up-icon>
                <view class="hover-box" v-if="showMoreBtn">
                    <view class="box-item" @click="changeDevice">解绑设备</view>
                </view>
            </view>
        </view>
        <view v-if="props.dataMap.batteryState" class="body">
            <!-- 表示充电中 -->
            <ScreenPower :dataMap="props.dataMap"></ScreenPower>
        </view>
        <view v-else class="body">
            <view class="left">
                <image class="img" :src="imageUrl" mode="aspectFit"></image>
                
                <view class="text text-01" style="padding-bottom: 6rpx;">{{ deviceNo }}</view>
                <view class="text" v-if="exceedCountDown > 0 && deviceMeasureStatus === 20">{{ exceedCountDown }}s</view>
                <view class="text" v-else-if="taskMeasureInterval">{{ taskMeasureInterval }}一次</view>
            </view>
            <view class="right">
                <view v-if="onlineState === 0" class="close-box wifi-box">
                    <!-- 已关机 -->
                    <image class="img" src="/static/img/not_start.png" mode="aspectFit"></image>
                    <view class="text">设备已关机，请长按开机</view>
                </view>
                <view v-else-if="onlineState === 3" class="close-box wifi-box">
                    <!-- 断网 -->
                    <image class="img" src="/static/img/not_wifi.png" mode="aspectFit"></image>
                    <view class="text">已断网，请检查网络</view>
                </view>
                <template v-else>
                    <!-- 在线、休眠 -->
                    <!-- 显示空闲、测量中、测量结果、休眠中(体温计预热) -->

                    <!-- 空闲 -->
                    <!-- 单次测量，显示启动测量，与测量完成页面一样 -->
                    <!-- 连续测量，显示倒计时测量间隔,显示启动按钮 -->

                    <template v-if="props.dataMap.workingMode == 1" >
                        <!-- 单次测量 -->
                        <Measuring :dataMap="props.dataMap" :curMeasuring="curMeasuring">
                            <!-- {{ showBtn }} 设备状态{{ deviceMeasureStatus }}, 在线状态{{ onlineState }} -->
                            <view v-if="showBtn" style="display: flex;">
                                <template v-if="props.dataMap.groupCode === 'HYO2'">
                                    <view class="btn-start" @click.stop="onStartMeasure(1)">测血压</view>
                                    <view class="btn-start" @click.stop="onStartMeasure(2)" style="margin-left: 16rpx;">测血氧</view>  
                                </template>
                                <view v-else class="btn-start" @click.stop="onStartMeasure(0)">启动测量</view>
                            </view>
                        </Measuring>
                    </template>
                    <template v-else>
                        <!-- 连续测量 -->
                        <view v-if="deviceMeasureStatus === 30" class="wait-box">
                            <!-- 休眠中 -->
                             <view style="height: 160rpx; width: 160rpx; margin-bottom: 14rpx;position: relative">
                                 <AnimationLoading :nextMeasureTime="props.dataMap.nextMeasureTime"/>
                             </view>
                            <view>下次测量时间 {{ nextMeasureTime }}</view>
                            <view v-if="showBtn" style="display: flex;">
                                <template v-if="props.dataMap.groupCode === 'HYO2'">
                                    <view class="btn-start" @click.stop="onStartMeasure(1)">测血压</view>
                                    <view class="btn-start" @click.stop="onStartMeasure(2)" style="margin-left: 16rpx;">测血氧</view>  
                                </template>
                                <view v-else class="btn-start" @click.stop="onStartMeasure(0)">启动测量</view>
                            </view>
                         </view>
                         <view v-else-if="deviceMeasureStatus === 2" class="wait-box">
                            <!-- 体温测量中 -->
                            <view class="text-value">{{ curMeasuring.temp }}<text class="unit" v-if="curMeasuring.temp">°C</text></view>
                            <LoadingBar :percent="75" style="height: 48rpx; width: 264rpx; margin-bottom: 14rpx;"></LoadingBar>
                            <view>预热中，请耐心等待</view>
                         </view>
                         <Measuring v-else :dataMap="props.dataMap" :curMeasuring="curMeasuring">
                            <!-- 测量中、测量完成 -->
                            <view v-if="showBtn" style="display: flex;">
                                <template v-if="props.dataMap.groupCode === 'HYO2'">
                                    <view class="btn-start" @click.stop="onStartMeasure(1)">测血压</view>
                                    <view class="btn-start" @click.stop="onStartMeasure(2)" style="margin-left: 16rpx;">测血氧</view>  
                                </template>
                                <view v-else class="btn-start" @click.stop="onStartMeasure(0)">启动测量</view>
                            </view>
                        </Measuring>
                    </template>
                </template>
            </view>
        </view>
    </view>
</template>
<script setup>
    // device.measureStatus = 测量状态: 0=空闲；1=血氧测量中；2=体温测量中；3=血压测量中；20=测量完成；30=休眠中；40=关机中
    // onlineState   = 在线状态 0 关机 1在线 2休眠 3断网
    // measureWay    = 测量方式：1=单次测量；2=连续测量； 改为 workingMode 属性

    // 单次测量没有休眠，只有启动测量(空闲,显示按钮),测量中(不显示按钮)，测量完成(显示按钮)
            // 空闲     ：启动按钮+结果值
            // 测量中   ：动画效果，没有测量时间
            // 等待     ：（没有）
            // 测量完成 ： 与空闲一样

    // 连续测量, 空闲、测量完成(显示倒计时20秒) 显示按钮。
            // 空闲    ： (没有)
            // 测量中  ： 与点测量一样，显示测量中动画
            // 测量完成：显示测量完成，倒计时20秒
            // 等待    ：显示休眠界面倒计时休眠时间 在线1、等待30 = 等待界面。等待2、等待30 = 休眠页面

    // 测量完成，倒计时20秒
    // 等待中显示，修改测量频率

    // 体温，没有启动按钮
	import moment from 'moment';
    import { startMeasure } from '@/common/api/device'
    import { ref, computed, watch, defineProps, defineEmits } from 'vue'
    import ElectricQuantity from '@/components/animation/ElectricQuantity.vue'; // 电池
    import AnimationLoading from '@/components/animation/AnimationLoading.vue'; // 等待休眠
    import LoadingBar from '@/components/animation/LoadingBar.vue'; // 预热
    import ScreenPower from './ScreenPower.vue'; // 充电
    import Measuring from './Measuring.vue'; // 测量显示
    import { useUserInfo } from '@/stores/userInfo'
    import { storeToRefs } from 'pinia'

    const storeUserInfo = useUserInfo();
    const { userInfos } = storeToRefs(storeUserInfo);

    const props = defineProps({
        dataMap: Object,   // 设备信息
    })

    const emit = defineEmits(['changeDevice'])

    const showMoreBtn = ref(true)
    function changeDevice() {
        emit('changeDevice', props.dataMap)
        showMoreBtn.value = false
        
        setTimeout(() => {
            showMoreBtn.value = true
        }, 300);
    }

    // 设备图片
    import device1 from '/static/products/thermometer02.png';
    import device2 from '/static/products/blood-pressure02.png';
    import device3 from '/static/products/blood-pressure01.png';
    import device4 from '/static/products/bool-oxygen01.png';
    const imageDeviceList = {
        BCB : "充电盒",
        DRIP : "点滴监护仪",
        HYO2 : device3,
        SPHY : device2,
        SPO2 : device4,
        THERMOMETER : device1,
        THERMOMETERSPLINCHED : device1
    }
    const imageUrl = computed(() => {
        return imageDeviceList[props.dataMap.groupCode]
    })

    const curMeasuring = computed(() => {
        if (!props.dataMap.latestMeasure) {
            return {
                deviceId: '', 
                measureType: 0, 
                measureTime: "", 
                measureStatus: 0, 
                measureMsg: "", 
                dpb: 0, 
                sbp: 0, 
                pulse: 0, 
                heartbeatState: 0, 
                spoz: null, 
                hr: null, 
                temp: null, 
                currentBp: null, 
                currentTemp: null, 
                deviceTaskId: 0, 
                taskId: null
            }
        }
        return JSON.parse(props.dataMap.latestMeasure)
    })

    // 获取设备编号
    const deviceNo = computed(() => {
        // 获取后四位字符
        const deviceNo = props.dataMap?.deviceNo?.slice(-4)
        return deviceNo
    })

    // 设备状态 0 关机 1在线 2休眠 3断网
    const onlineState = computed(() => {
        const onlineState = props.dataMap.onlineState || 0
        
        return onlineState
    })

    // 显示下次测量时间
    const nextMeasureTime = computed(() => {
        if (!props.dataMap.nextMeasureTime) {
            return '--:--'
        }
        return moment(props.dataMap.nextMeasureTime).format('HH:mm')
    })

    const taskMeasureInterval = computed(() => {
        if (props.dataMap.workingMode === 1) {
            return ''
        }
        
        const a = props.dataMap.measureInterval 
        
        if (a === null) {
            return "- 秒"
        }

        if (a < 60) {
            return a + "秒"; // 小于60秒直接显示秒数
        } else if (a < 3600) {
            // 超过60秒但小于1小时，显示分钟
            const minutes = Math.floor(a / 60);
            return minutes + "分钟";
        } else {
            // 超过1小时，显示小时和剩余的分钟
            const hours = Math.floor(a / 3600);
            const remainingMinutes = Math.floor((a % 3600) / 60);
            
            // 返回 "X小时" 或者 "X小时Y分钟"
            return remainingMinutes > 0 ? hours + "小时" + remainingMinutes + "分钟" : hours + "小时";
        }
    })

    // 设备测量进行时状态
    const deviceMeasureStatus = computed(() => {
        if (props.dataMap.measureStatus === null) {
            return 0
        }
        return props.dataMap.measureStatus
    })

    // 显示按钮
    const showBtn = computed(() => {
        // 设备不在线
        if (onlineState.value !== 1) {
            return false
        }

        // 如果是体温就不显示
        if (props.dataMap.groupCode === 'THERMOMETER') {
            return false
        }

        // 状态处于，空闲、测量完成、等待
        if (deviceMeasureStatus.value === 0 || deviceMeasureStatus.value === 20 || deviceMeasureStatus.value === 30) {
            return true
        }
        return false
    })


    const exceedCountDown = ref(0)
    const exceedTimer = ref(null)
    const updateCountdown = () => {

        if (!curMeasuring.value.measureTime) {
            return 0
        }

        const startTime = new Date(curMeasuring.value.measureTime).getTime();
        const countdownDuration = 20 * 1000; // 20秒
        const currentTime = new Date().getTime();
        const elapsedTime = currentTime - startTime;
        const remainingTime = countdownDuration - elapsedTime;
        
        exceedCountDown.value = Math.max(Math.floor(remainingTime / 1000), 0);
        if (exceedCountDown.value > 0) {
            exceedTimer.value = setTimeout(updateCountdown, 1000);
        }
    };

    // 监听测量状态
    watch(() => deviceMeasureStatus.value, (val) => {
        // 状态为测量中
        if (val === 20) {
            // 测量完成
            clearTimeout(exceedTimer.value);
            updateCountdown();
        }
    }, { immediate: true })

    const onStartMeasure = (val) => {


        console.log(userInfos.value)
        const params = {
            userId: userInfos.value.userId,
            personId: userInfos.value.personId,
            personName: userInfos.value.personInfo.personName,
            deviceNo: props.dataMap.deviceNo
        }
        if (val) {
            params.measureType = val
        }

        // 改变显示家庭成员
        let lastUserInfo = uni.getStorageSync("lastSelectedUserInfo");
        if (lastUserInfo && lastUserInfo.personId !== undefined && lastUserInfo.personId !== null) {
            params.personId = lastUserInfo.personId
        }

        startMeasure(params).then(res => {
            if (!res.success) {
                uni.showToast({
                    title: '无法启动！',
                    icon: 'error'
                })
            }else {
                uni.showToast({
                    title: '启动成功！',
                    icon: 'success'
                })
            }
        })
    }
</script>
<style lang="scss" scoped>
.container-device {
    padding: 16rpx 0 0;
    background: $xx-color-primary;
    border-radius: 24rpx;
    color: white;
    height: 100%;
    display: flex;
    flex-direction: column;
    margin-top: 24rpx;
    &.no-online {
        background: #ffffff;
        color: #1A202C;
        .body {
            height: 400rpx;
            .left .text-01 {
                color: #666;
            }
        }
    }
    .head {
        padding: 0 12rpx 0 0;
        .name, .title {
            font-size: 28rpx;
        }
        .title {
            min-width: 220rpx;
            text-align: center;
        }
        .status {
            .dot {
                width: 24rpx;
                height: 24rpx;
                background: #43cf7c;
                border-radius: 50%;
                margin-right: 12rpx;
            }
            .blink {
                animation: blink1 3s infinite;
            }
            .iconfont {
                font-size: 36rpx;
            }
        }
    }
    .body {
        display: flex;
        flex-direction: row;
        height: 400rpx;
        .left {
            width: 220rpx;
            height: 100%;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            overflow: hidden;
            .img {
                height: 240rpx;
                // width: 120%;
                // height: 100%;
            }
            .text {
                font-size: 28rpx;
            }
        }
        .right {
            display: flex;
            flex-direction: column;
            position: relative;
            flex: 1;
            z-index: 1;
            overflow: hidden;
            .tip {
                position: absolute;
                left: 0;
                top: 0;
                width: 100%;
                height: 100%;
                background: $xx-color-primary;
                text-align: center;
                display: flex;
                flex-direction: column;
                justify-content: center;
                font-size: 34rpx;
                color: #ff9900;
                .label {
                    font-size: 28rpx;
                    padding-bottom: 14rpx;
                }
            }
            
            .close-box, .wifi-box {
                width: 100%;
                height: 100%;
                display: flex;
                flex-direction: column;
                justify-content: center;
                align-items: center;
                .img {
                    height: 140rpx;
                }
                .text {
                    color: #718096;
                    padding-top: 10rpx;
                }
            }
        }
        .wait-box {
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            height: 100%;
            .text {
                height: 100rpx;
                color: #E4F2F7;
                padding-top: 10rpx;
            }
            .text-value {
                height: 100rpx;
                font-size: 68rpx;
                color: #33DFEE;
                .unit {
                    font-size: 32rpx;
                    padding-left: 6rpx;
                }
            }
            .btn-start {
                margin-top: 24rpx;
            }
        }
    }
}
.btn-start {
    width: 154rpx;
    height: 52rpx;
    line-height: 52rpx;
    border-radius: 8rpx;
    background: #D8A02D;
    color: white;
    text-align: center;
    font-size: 24rpx;

}
@keyframes blink1 {
    0% {
        opacity: 1;
    }
    50% {
        opacity: 0.4;
    }
    100% {
        opacity: 1;
    }
}
.xx-flex-center {
    display: flex;
    justify-content: center;
    align-items: center;
}
.flex-row-between {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
}
.hover-more {
    position: relative;
    margin-left: 40rpx;
    .hover-box {
        display: none;
        position: absolute;
        right: 0;
        font-size: 28rpx;
        background: rgba(255, 255, 255, 1);
        box-shadow: rgba(0, 0, 0, 0.15) 0px 5px 15px 0px;
        border-radius: 8rpx;
        text-align: center;
        color: #0e9cd8;
        width: 200rpx;
        z-index: 2;
        animation: fadeIn 0.3s;
        .box-item {
            padding: 16rpx;
            border-bottom: 1px solid #d8d8d8;
            &:last-child {
                border-bottom: none;
            }
        }
    }
    &:hover {
        .hover-box {
            display: block;
            
        }
    }
}
</style>