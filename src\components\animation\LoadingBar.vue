<template>
    <view class="processbar" :style="oStyle">
        <view class="processbar1" :style="barStyle">
            <view class="processbar2"></view>
            <view class="processbar3"></view>
        </view>
        <slot></slot>
    </view>
</template>
<script setup>
import { ref, reactive, onMounted, getCurrentInstance, watch, computed, nextTick } from 'vue';

const props = defineProps({
    percent: [Number, String]
})

const oStyle = reactive({})
const barStyle = reactive({
    width: ''
})

const instance = getCurrentInstance();

onMounted(() => {
    const query = uni.createSelectorQuery().in(instance.proxy);
    query.select('.processbar').boundingClientRect((rect) => {
        // console.log(`宽度: ${rect.width}px, 高度: ${rect.height}px`)
        oStyle.width = rect.width + 'px'
        oStyle.height = rect.height + 'px'
    }).exec();

    watch(() => props.percent, val => {
        barStyle.width = val + '%'
    }, {
        immediate: true
    })
})
</script>


<style lang="scss" scoped>
.processbar {
    position: relative;
    z-index: 1;
    border-radius: 66rpx;
    border: 2rpx solid;
    border-color: rgba(21, 156, 173, 1);
    box-sizing: border-box;
    display: flex;
    flex-shrink: 0;
    flex-grow: 0;
    flex-basis: auto;
    .processbar1 {
        position: relative;
        width: 50%;
        max-width: calc(100% - 16rpx);
        height: calc(100% - 16rpx);
        top: 8rpx;
        left: 8rpx;
        z-index: 1;
        border-radius: 66rpx;
        background: linear-gradient(-90deg, #33DFEE 41.67%, #33DFEE09 100%);
    }

    .processbar2 {
        position: absolute;
        width: 80%;
        height: 100%;
        top: 0;
        right: 0;
        z-index: 2;
        border-bottom-right-radius: 66rpx;
        border-top-right-radius: 66rpx;
        background-size: 50px;
        background-image: linear-gradient(-90deg, rgba($color: #00ffff, $alpha: .15) 25%, transparent 25%,
                transparent 50%, rgba($color: #fff, $alpha: .15) 50%,
                rgba($color: #00ffff, $alpha: .15) 75%, transparent 75%,
                transparent);
        animation: animate-stripes 2s linear infinite;
    }


    .processbar3 {
        position: absolute;
        width: 20%;
        max-width: 32rpx;
        height: 100%;
        top: 0;
        right: 0;
        z-index: 2;
        border-bottom-right-radius: 66rpx;
        border-top-right-radius: 66rpx;
        box-shadow: 0 0 8rpx #33DFEE;
    }
}



@keyframes animate-stripes {
    0% {
        background-position: 0 0;
    }

    100% {
        background-position: -150px 0;
    }
}
</style>