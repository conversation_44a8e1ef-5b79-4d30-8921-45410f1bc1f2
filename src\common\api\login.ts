import request from '@/common/request'
import type{ ILogin } from '@/models/login'

/**
 * 登录
 * @param data 请求体参数
 */
export const signIn = (data : ILogin) => {
	return request({
		method: 'POST',
		url: '/auth/login/appLogin',
		body: data,
		hideLoading: false,
		custom: {
			ignoreRespInterceptors: true //忽略本次请求的响应拦截
		}
	})
}
//按ID获取用户信息 /auth/user/getUserById
export const getUserInfo = () => {
	return request({
		method: 'POST',
		url: '/auth/user/getUserById',
		hideLoading: false,
		custom: {
			ignoreRespInterceptors: true //忽略本次请求的响应拦截
		}
	})
}

/**
 * 查询所有字典和字典项
 */
export const queryAllDictAndItem = () => {
	return request({
		method: 'POST',
		url: '/auth/system/dict/queryAllDictAndItem',
		hideLoading: false,
	})
}
