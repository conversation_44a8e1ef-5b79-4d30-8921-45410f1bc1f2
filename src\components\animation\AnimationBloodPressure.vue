<template>
    <view class="blood-pressure-monitor" :style="{ width: width, height: height }">

        <view class="min" :style="{ height:  (parseInt(barHeight) * 2) + 'rpx' }">
            <text>90</text>
            <!-- <text class="line"></text> -->
        </view>
        <view v-for="(bar, index) in renderList" :key="index" class="bar" :style="{ height: barHeight }" :class="{ 'active': bar === 1, 'first': index === 0, 'last': index === renderList.length - 1 }"></view>
        <view class="max" :style="{bottom: (parseInt(height) / 2 + 6) + 'rpx' }">
            <text>140</text>
            <!-- <text class="line"></text> -->
        </view>
    </view>
</template>

<script setup>
import { ref, watch, defineProps } from 'vue'

const props = defineProps({
    curMeasuring: Object,
    width: {
        type: String,
        default: '26rpx'
    },
    height: {
        type: String,
        default: '334rpx'
    },
    barHeight: {
        type: String,
        default: '20rpx'
    }
})

const ranges = [
    [0, 44],
    [45, 89],
    [90, 106],
    [107, 123],
    [124, 139],
    [140, 159],
    [160, 179],
    [180, 240]
];
const mapRangeToBinary = (obj) => {

    let max = 0
    let min = obj.dpb || 0 // 舒张压

    // 收缩压
    if (obj.measureStatus === 2) {
        max = obj.currentBp || 0 // 气袋压
    }else {
        max = obj.sbp || 0
    }

    // 遍历每个区间，判断是否在给定的 min 和 max 范围内
    return ranges.map(range => {
        const [rangeMin, rangeMax] = range;
        // 如果当前区间与 min-max 区间有交集，返回 1；否则返回 0
        if (max >= rangeMin && min <= rangeMax) {
            return 1;
        }
        return 0;
    });
}

const renderList = ref([]);
watch(() => props.curMeasuring, (val) => {
    renderList.value = mapRangeToBinary(val).filter(item => item === 1);
}, { immediate: true })

</script>
<style lang="scss" scoped>
.blood-pressure-monitor {
    display: flex;
    flex-direction: column-reverse;
    align-items: center;
    margin: 0 14rpx 0 20rpx;
    height: 224rpx;
    width: 26rpx;
    border: 1px solid #d2d2d2;
    border-radius: 14rpx;
    position: relative;
    .bar {
        width: 18rpx;
        height: 20rpx;
        margin: 8rpx 0 0;
        background: none;
        &.active {
            background: #888;
            // 用 css 知道最后一个 active
            &.last {
                animation: blink 2s infinite;
            }
        }
        &.first {
            border-radius: 0 0 6rpx 6rpx;
            margin-bottom: 6rpx;
        }
        // &:nth-child(4) {
        //     margin: 4rpx 0 0 0;
        // }
        // &:nth-child(5) {
        //     margin: 4rpx 0 4rpx 0;
        // }
        // &:nth-child(6) {
        //     margin: 8rpx 0 4rpx 0;
        // }
    }
    .min, .max {
        position: absolute;
        right: 35rpx;
        font-size: 24rpx;
        height: 40rpx;
        color: #666;
        .line {
            position: absolute;
            top: 17rpx;
            right: -34rpx;
            width: 24rpx;
            height: 1px;
            background: #d2d2d2;
        }
    }
    .min {
        bottom: 28rpx;
        .line {
            top: 16rpx;
        }
    }
    .max {
        bottom: 112rpx;
        .line {
            // top: 21rpx;
        }
    }
}
@keyframes blink {
    0% {
        opacity: 1;
    }
    50% {
        opacity: 0;
    }
    100% {
        opacity: 1;
    }
}
</style>