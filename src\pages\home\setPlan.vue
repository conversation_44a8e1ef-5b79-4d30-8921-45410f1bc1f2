<template>
  <view class="bg-[#e3ecf2] p-[30rpx]">
    <up-navbar
        :autoBack="true"
        :placeholder="true"
        bgColor="#fff"
        height="82rpx"
        title="设置计划"
        >
        <template #right>
          <view class="p-[10rpx]" @click="showSettings = !showSettings">
            <uni-icons :type="showSettings ? 'eye-slash' : 'gear'" size="20" color="#666"></uni-icons>
          </view>
        </template>
    </up-navbar>


    <!-- 日间测量 -->
    <view class="text-[34rpx]">日间测量</view>

    <view class="px-[30rpx] bg-white rounded-[16rpx] mt-[20rpx]">
      <picker-view :indicator-style="indicatorStyle" :value="formData.rjmeasureInterval" @change="bindChangeRJ"
        class="picker-view">
        <picker-view-column>
          <view class="picker-item" v-for="(item, index) in measurementInterval" :key="index">每隔 {{ item.label }} 测量
          </view>
        </picker-view-column>
      </picker-view>
    </view>
    <!-- 日间设置区域 -->
    <view v-if="showSettings" class="bg-white rounded-[16rpx] mt-[20rpx] p-[30rpx]">
      <view class="mb-[20rpx] flex items-center">
        <text class="mr-[40rpx]">测量时间:</text>
        <view class="flex items-center h-[80rpx]" @click="onClickShowDayTime">
          <view class="flex-1">{{ formData.rjStartTime }}-{{ formData.rjEndTime }}</view>
          <text class="ml-[20rpx] text-[#666] text-[24rpx]">(自动跟随夜间时间)</text>
        </view>
      </view>
      <view class="mb-[20rpx] flex items-center">
        <text class="mr-[20rpx]">音量设置(0-15):</text>
        <slider class="flex-1" :value="daySettings.volume" :min="0" :max="15" :step="1"
          @change="(e: any) => daySettings.volume = e.detail.value" show-value />
      </view>
      <view class="mb-[20rpx] flex items-center">
        <text class="mr-[20rpx]">收缩压范围:</text>
        <view class="flex-1 flex items-center mt-[10rpx]">
          <input type="number" v-model="daySettings.lowerSbp" class="border p-[10rpx] w-[150rpx] text-center" />
          <text class="mx-[20rpx]">-</text>
          <input type="number" v-model="daySettings.upperSbp" class="border p-[10rpx] w-[150rpx] text-center" />
        </view>
      </view>
      <view class="mb-[20rpx] flex items-center">
        <text class="mr-[20rpx]">舒张压范围:</text>
        <view class="flex items-center mt-[10rpx]">
          <input type="number" v-model="daySettings.lowerDbp" class="border p-[10rpx] w-[150rpx] text-center" />
          <text class="mx-[20rpx]">-</text>
          <input type="number" v-model="daySettings.upperDbp" class="border p-[10rpx] w-[150rpx] text-center" />
        </view>
      </view>
      <view class="mb-[20rpx]">
        <view class="py-[16px] text-[32rpx]">提醒设置</view>
        <checkbox-group @change="onDayPreSettingChange">
          <label v-for="(item, index) in preSettingOptions" :key="index" class="block mb-[10rpx]">
            <checkbox :value="index.toString()" :checked="daySettings.preSetting[index] === 1" />
            {{ item }}
          </label>
        </checkbox-group>
      </view>
    </view>

    <!-- 夜间测量 -->
    <view class="text-[34rpx] mt-[40rpx]">夜间测量</view>
    <view class="px-[30rpx] bg-white rounded-[16rpx] mt-[20rpx]">
      <view class="h-[120rpx] flex items-center justify-between">
        <text>夜间区间</text>
        <view @click="onClickShowTime" class="flex-1 h-full center justify-end">
          {{ formData.yjStartTime }}-{{ formData.yjEndTime }}
        </view>
      </view>
      <view class="h-[1px] w-full bg-[#DDDDDD]"></view>
      <picker-view :indicator-style="indicatorStyle" :value="formData.yjmeasureInterval" @change="bindChangeYJ"
        class="picker-view">
        <picker-view-column>
          <view class="picker-item" v-for="(item, index) in measurementInterval" :key="index">每隔 {{ item.label }} 测量
          </view>
        </picker-view-column>
      </picker-view>
    </view>
    <!-- 夜间设置区域 -->
    <view v-if="showSettings" class="bg-white rounded-[16rpx] mt-[20rpx] p-[30rpx]">
      <view class="mb-[20rpx] flex items-center">
        <text class="mr-[20rpx]">音量设置(0-15):</text>
        <slider class="flex-1" :value="nightSettings.volume" :min="0" :max="15" :step="1"
          @change="(e: any) => nightSettings.volume = e.detail.value" show-value />
      </view>
      <view class="mb-[20rpx] flex items-center">
        <text class="mr-[20rpx]">收缩压范围:</text>
        <view class="flex items-center mt-[10rpx]">
          <input type="number" v-model="nightSettings.lowerSbp" class="border p-[10rpx] w-[150rpx]" />
          <text class="mx-[20rpx]">-</text>
          <input type="number" v-model="nightSettings.upperSbp" class="border p-[10rpx] w-[150rpx]" />
        </view>
      </view>
      <view class="mb-[20rpx] flex items-center">
        <text class="mr-[20rpx]">舒张压范围:</text>
        <view class="flex items-center mt-[10rpx]">
          <input type="number" v-model="nightSettings.lowerDbp" class="border p-[10rpx] w-[150rpx]" />
          <text class="mx-[20rpx]">-</text>
          <input type="number" v-model="nightSettings.upperDbp" class="border p-[10rpx] w-[150rpx]" />
        </view>
      </view>
      <view class="mb-[20rpx]">
        <view class="py-[16px] text-[32rpx]">提醒设置</view>
        <checkbox-group @change="onNightPreSettingChange">
          <label v-for="(item, index) in preSettingOptions" :key="index" class="block mb-[10rpx]">
            <checkbox :value="index.toString()" :checked="nightSettings.preSetting[index] === 1" />
            {{ item }}
          </label>
        </checkbox-group>
      </view>
    </view>


    <!-- 血氧 -->
    <view class="flex justify-between text-[34rpx] mt-[40rpx]">
        <text>血氧</text>
        <view class="flex text-[28rpx] items-center">
            血氧{{ startOximetry ? '开启' : '关闭' }}：<up-switch v-model="startOximetry" asyncChange @change="asyncChangeStartOximetry" inactiveColor="#F9AE3D" activeColor="#5ac725"></up-switch>
        </view>
    </view>
    <!-- 血氧设置区域 -->
    <view v-if="showSettings" class="bg-white rounded-[16rpx] mt-[20rpx] p-[30rpx]">
      <view class="mb-[20rpx] flex items-center">
        <text class="mr-[40rpx]">测量时间</text>
        <view class="flex items-center mt-[10rpx] h-[80rpx]" style="line-height: 80rpx;" @click="onClickShowOxygenTime">
          <view class="flex-1">{{ oxygenSettings.startTime }}-{{ oxygenSettings.endTime }}</view>
          <text class="ml-[20rpx] text-[#666] text-[24rpx]">(默认跟随夜间时间)</text>
        </view>
      </view>

      <view class="mb-[20rpx] flex items-center">
        <text class="mr-[20rpx]">音量设置(0-15)</text>
        <slider class="flex-1" :value="oxygenSettings.volume" :min="0" :max="15" :step="1"
          @change="(e: any) => oxygenSettings.volume = e.detail.value" show-value />
      </view>

      <view class="mb-[20rpx]">
        <view class="my-[20rpx] mt-[40px]">测量间隔(分钟)</view>
        <picker-view :indicator-style="indicatorStyle"
          :value="[measurementInterval.findIndex(item => item.value === oxygenSettings.interval)]"
          @change="bindChangeOxygenInterval" class="picker-view h-[200rpx]">
          <picker-view-column>
            <view class="picker-item" v-for="(item, index) in measurementInterval" :key="index">每隔 {{ item.label }}
            </view>
          </picker-view-column>
        </picker-view>
      </view>

      <view class="mb-[20rpx]">
        <view class="py-[16px] text-[32rpx]">提醒设置</view>
        <checkbox-group @change="onOxygenPreSettingChange">
          <label v-for="(item, index) in oxygenPreSettingOptions" :key="index" class="block mb-[10rpx]">
            <checkbox :value="index.toString()" :checked="oxygenSettings.preSetting[index] === 1" />
            {{ item }}
          </label>
        </checkbox-group>
      </view>

      <view class="mb-[20rpx] flex items-center justify-between">
        <text>血氧下限</text>
        <input type="number" v-model="oxygenSettings.spo2Lower" class="border p-[10rpx] w-[150rpx] text-center" />
      </view>
      <view class="mb-[20rpx] flex items-center justify-between">
        <text>血氧下限持续时间(秒)</text>
        <input type="number" v-model="oxygenSettings.spo2LowerDuration" class="border p-[10rpx] w-[150rpx] text-center" />
      </view>
      <view class="mb-[20rpx] flex items-center justify-between">
        <text>血氧严重过低下限</text>
        <input type="number" v-model="oxygenSettings.spo2SevereLower" class="border p-[10rpx] w-[150rpx] text-center" />
      </view>
      <view class="mb-[20rpx] flex items-center justify-between">
        <text>血氧严重过低持续时间(秒)</text>
        <input type="number" v-model="oxygenSettings.spo2SevereLowerDuration" class="border p-[10rpx] w-[150rpx] text-center" />
      </view>
      <view class="mb-[20rpx] flex items-center justify-between">
        <text>脉率上限</text>
        <input type="number" v-model="oxygenSettings.prUpper" class="border p-[10rpx] w-[150rpx] text-center" />
      </view>
      <view class="mb-[20rpx] flex items-center justify-between">
        <text>脉率下限</text>
        <input type="number" v-model="oxygenSettings.prLower" class="border p-[10rpx] w-[150rpx] text-center" />
      </view>
      <view class="mb-[20rpx] flex items-center justify-between">
        <text>血氧血压联动下限</text>
        <input type="number" v-model="oxygenSettings.o2PulseRateLower" class="border p-[10rpx] w-[150rpx] text-center" />
      </view>
      <view class="mb-[20rpx] flex items-center justify-between">
        <text>血氧血压联动下限持续时间(秒)</text>
        <input type="number" v-model="oxygenSettings.o2PulseRateLowerDuration" class="border p-[10rpx] w-[150rpx] text-center" />
      </view>
      <view class="mb-[20rpx] flex items-center justify-between">
        <text>脉率血压联动下限</text>
        <input type="number" v-model="oxygenSettings.o2PulseRateUpper" class="border p-[10rpx] w-[150rpx] text-center" />
      </view>
      <view class="mb-[20rpx] flex items-center justify-between">
        <text>脉率血压联动下限持续时间(秒)</text>
        <input type="number" v-model="oxygenSettings.o2PulseRateUpperDuration" class="border p-[10rpx] w-[150rpx] text-center" />
      </view>
    </view>
    <view v-else class="p-[30rpx] bg-white rounded-[16rpx] mt-[20rpx] color-[#333]">睡前2小时启动血氧测量</view>
    <view class="h-160rpx"></view>

    <view class="w-full h-140rpx fixed bottom-[0] left-0 bg-white flex justify-center z-3"
      style="border-top: 1px solid #e3e3e3;">
      <view class="h-[80rpx] w-[80%] leading-[80rpx] bg-[#237FC2] color-white rounded-[50rpx] text-center mt-[20rpx]"
        @click="onClickSave">保存</view>
    </view>

    <up-popup :show="showPopup" round="2" @close="showPopup = false">
      <TimeRange ref="timeRange" @cancle="cancle" @confirm="confirm"></TimeRange>
    </up-popup>

    <up-popup :show="showOxygenPopup" round="2" @close="showOxygenPopup = false">
      <TimeRange ref="oxygenTimeRange" @cancle="cancleOxygenTime" @confirm="confirmOxygenTime"></TimeRange>
    </up-popup>

    <!-- 添加日间时间选择弹窗 -->
    <up-popup :show="showDayPopup" round="2" @close="showDayPopup = false">
      <TimeRange ref="dayTimeRange" @cancle="cancleDayTime" @confirm="confirmDayTime"></TimeRange>
    </up-popup>
  </view>
</template>
<script setup lang="ts">
import { ref, reactive, onMounted, onBeforeUnmount, computed, nextTick, watch } from 'vue';
import { onLoad, onShow, onUnload } from "@dcloudio/uni-app";
import { getPatientBindingDevice, startHomeMeasure, updateMeasureStatus } from '@/common/api/task';
import moment from 'moment';
import TimeRange from '@/components/TimeRange.vue';
import { useTask } from '@/stores/task'

import { useUserInfo } from '@/stores/userInfo';
const storeUserInfo = useUserInfo();

const storeUseTask = useTask();
const showPopup = ref(false)
const showSettings = ref(false)
const showOxygenPopup = ref(false)
const oxygenTimeRange = ref()
const showDayPopup = ref(false)
const dayTimeRange = ref()

const measurementInterval = computed(() => {
    const list = storeUserInfo.dictCodeInfo?.measurementInterval
    if (list) {
        return list.map((item: any) => {
            return {
                value: item.itemValue,
                label: item.itemName
            }
        })
    }
    return [{ value: 2, label: '2分钟' }, { value: 5, label: '5分钟' }, { value: 10, label: '10分钟' }, { value: 15, label: '15分钟' }, { value: 30, label: '30分钟' }, { value: 45, label: '45分钟' }, { value: 60, label: '60分钟' }]
})

const indicatorStyle = `height: 50px;`
const timeRange = ref()

const formData = ref({
  yjStartTime: '22:00',
  yjEndTime: '08:00',
  rjStartTime: '08:00',
  rjEndTime: '22:00',
  yjmeasureInterval: [4],
  rjmeasureInterval: [3]
})

const preSettingOptions = [
  '提前10分钟启动语音提醒',
  '提前5分钟启动语音提醒',
  '测试前启动语音提醒',
  '播报测量结果',
  '测量结束后语音提醒下次测量时间',
  '允许手动测量'
]

const daySettings = reactive({
  volume: 4,
  upperSbp: 140,
  lowerSbp: 90,
  upperDbp: 90,
  lowerDbp: 60,
  preSetting: [0, 0, 0, 0, 0, 1]
})

const nightSettings = reactive({
  volume: 0,
  upperSbp: 140,
  lowerSbp: 90,
  upperDbp: 90,
  lowerDbp: 60,
  preSetting: [0, 0, 0, 0, 0, 0]
})

const oxygenPreSettingOptions = [
  '未定义',
  '未定义',
  '测试前启动语音提醒',
  '播报测量结果',
  '测量结束后语音提醒下次测量时间',
  '未定义'
]

const oxygenSettings = reactive({
  startTime: '',
  endTime: '',
  interval: 2,
  volume: 0,
  preSetting: [0, 0, 0, 0, 0, 0],
  spo2Lower: 90,
  spo2LowerDuration: 600,
  spo2SevereLower: 85,
  spo2SevereLowerDuration: 300,
  prUpper: 180,
  prLower: 40,
  o2PulseRateLower: 90,
  o2PulseRateLowerDuration: 150,
  o2PulseRateUpper: 95,
  o2PulseRateUpperDuration: 300
})

function onClickShowTime() {
  showPopup.value = true
  setTimeout(() => {
    timeRange.value.setDefault(formData.value.yjStartTime, formData.value.yjEndTime)
  }, 0);
}

function cancle() {
  showPopup.value = false
}
function confirm(e: any) {
  formData.value.yjStartTime = e.start_time
  formData.value.yjEndTime = e.end_time
  showPopup.value = false

  // 更新血氧时间
  oxygenSettings.startTime = moment('2025-01-01 ' + e.start_time).subtract(2, 'hours').format('HH:mm')
  oxygenSettings.endTime = e.end_time

  // 更新日间时间
  formData.value.rjStartTime = e.end_time
  formData.value.rjEndTime = e.start_time
}

interface Device {
  patientId: number,
  planJson: '',
  deviceId: any
}

const deviceInfo = ref<Device>({
  patientId: 0,
  planJson: '',
  deviceId: ''
})

const bindChangeYJ = (e: any) => {
  formData.value.yjmeasureInterval = e.detail.value
}

const bindChangeRJ = (e: any) => {
  formData.value.rjmeasureInterval = e.detail.value
}

function onDayPreSettingChange(e: any) {
  const values = e.detail.value.map(Number)
  daySettings.preSetting = Array(6).fill(0)
  values.forEach((index: any) => {
    daySettings.preSetting[index] = 1
  })
}

function onNightPreSettingChange(e: any) {
  const values = e.detail.value.map(Number)
  nightSettings.preSetting = Array(6).fill(0)
  values.forEach((index: number) => {
    nightSettings.preSetting[index] = 1
  })
}

function onOxygenPreSettingChange(e: any) {
  const values = e.detail.value.map(Number)
  oxygenSettings.preSetting = Array(6).fill(0)
  values.forEach((index: number) => {
    oxygenSettings.preSetting[index] = 1
  })
}

const bindChangeOxygenInterval = (e: any) => {
  const index = e.detail.value[0]
  oxygenSettings.interval = measurementInterval.value[index].value
}

watch(() => [formData.value.yjStartTime, formData.value.yjEndTime], ([newStartTime, newEndTime]) => {
  // 只在初始化时设置默认值
  if (!oxygenSettings.startTime || !oxygenSettings.endTime) {
    oxygenSettings.startTime = moment('2025-01-01 ' + newStartTime).subtract(2, 'hours').format('HH:mm')
    oxygenSettings.endTime = newEndTime
  }
}, { immediate: true })


const startOximetry = ref(true)

const asyncChangeStartOximetry = (e) => {
    uni.showModal({
        content: e ? '确定要启动血氧吗' : '确定要关闭血氧吗',
        success: (res) => {
        if (res.confirm) {
            startOximetry.value = e;
        }
        },
    });
}

const onClickSave = () => {
  const json = {
    s0081List: [
      {
        timeFrameName: '白天',
        startTime: formData.value.rjStartTime,
        endTime: formData.value.rjEndTime,
        interval: measurementInterval.value[formData.value.rjmeasureInterval[0]].value,
        preSetting: daySettings.preSetting,
        volume: daySettings.volume,
        upperSbp: daySettings.upperSbp,
        lowerSbp: daySettings.lowerSbp,
        upperDbp: daySettings.upperDbp,
        lowerDbp: daySettings.lowerDbp
      },
      {
        timeFrameName: '夜间',
        startTime: formData.value.yjStartTime,
        endTime: formData.value.yjEndTime,
        interval: measurementInterval.value[formData.value.yjmeasureInterval[0]].value,
        preSetting: nightSettings.preSetting,
        volume: nightSettings.volume,
        upperSbp: nightSettings.upperSbp,
        lowerSbp: nightSettings.lowerSbp,
        upperDbp: nightSettings.upperDbp,
        lowerDbp: nightSettings.lowerDbp
      }
    ],
    s0082List: [
      {
        ...oxygenSettings,
        timeFrameName: '夜间',
        startTime: oxygenSettings.startTime,
        endTime: oxygenSettings.endTime,
        interval: oxygenSettings.interval,
        preSetting: oxygenSettings.preSetting,
        volume: oxygenSettings.volume
      }
    ],
    measureStartTime: moment().format('YYYY-MM-DD HH:mm')
  }

  if (!startOximetry.value) {
    json.s0082List = []
  }

  const params = {
    beginDateTime: moment().format('YYYY-MM-DD HH:mm:ss'),
    endDateTime: moment().add(1, 'days').format('YYYY-MM-DD HH:mm:ss'),
    deviceId: deviceInfo.value.deviceId,
    patientId: deviceInfo.value.patientId,
    planJson: JSON.stringify(json),
  }

  startHomeMeasure(params).then((res: any) => {
    if (!res.success) {
      uni.showToast({ title: res.msg, icon: 'none' })
      return
    }
    // 进入家庭测量
    updateMeasureStatus({ measureStatus: 40, patientId: deviceInfo.value.patientId }).then(() => {
      uni.navigateBack({
        delta: 1,
        success: () => {
          storeUseTask.$state.isRefreshStatus = true
        }
      })
    })
  })
}

// 血氧时间选择相关函数
function onClickShowOxygenTime() {
  showOxygenPopup.value = true
  setTimeout(() => {
    oxygenTimeRange.value.setDefault(oxygenSettings.startTime, oxygenSettings.endTime)
  }, 0)
}

function cancleOxygenTime() {
  showOxygenPopup.value = false
}

function confirmOxygenTime(e: any) {
  oxygenSettings.startTime = e.start_time
  oxygenSettings.endTime = e.end_time
  showOxygenPopup.value = false
}

// 日间时间选择相关函数
function onClickShowDayTime() {
  showDayPopup.value = true
  setTimeout(() => {
    dayTimeRange.value.setDefault(formData.value.rjStartTime, formData.value.rjEndTime)
  }, 0)
}

function cancleDayTime() {
  showDayPopup.value = false
}

function confirmDayTime(e: any) {
  formData.value.rjStartTime = e.start_time
  formData.value.rjEndTime = e.end_time
  showDayPopup.value = false
}

onLoad(async (option: any) => {
  const res = await getPatientBindingDevice({ patientId: option.patientId })
  deviceInfo.value = res.data || {}

  if (deviceInfo.value.planJson) {
    const planData = JSON.parse(deviceInfo.value.planJson)
    //  通过 timeFrameName 判断yj rj
    let yj: any = {}
    let rj: any = {}
    planData.s0081List.forEach((item: any) => {
      if (item.timeFrameName === '夜间') {
        yj = item
      }
      if (item.timeFrameName === '白天') {
        rj = item
      }
    })

    // 夜间设置
    formData.value.yjStartTime = yj.startTime
    formData.value.yjEndTime = yj.endTime
    formData.value.yjmeasureInterval = [measurementInterval.value.findIndex((item: any) => item.value == yj.interval)]
    nightSettings.volume = yj.volume
    nightSettings.upperSbp = yj.upperSbp
    nightSettings.lowerSbp = yj.lowerSbp
    nightSettings.upperDbp = yj.upperDbp
    nightSettings.lowerDbp = yj.lowerDbp
    nightSettings.preSetting = yj.preSetting

    // 白天设置
    formData.value.rjStartTime = rj.startTime || yj.endTime
    formData.value.rjEndTime = rj.endTime || yj.startTime
    formData.value.rjmeasureInterval = [measurementInterval.value.findIndex((item: any) => item.value == rj.interval)]
    daySettings.volume = rj.volume
    daySettings.upperSbp = rj.upperSbp
    daySettings.lowerSbp = rj.lowerSbp
    daySettings.upperDbp = rj.upperDbp
    daySettings.lowerDbp = rj.lowerDbp
    daySettings.preSetting = rj.preSetting

    // 血氧设置
    if (planData.s0082List?.[0]) {
      const oxySetting = planData.s0082List[0]
      oxygenSettings.volume = oxySetting.volume
      oxygenSettings.interval = oxySetting.interval
      oxygenSettings.preSetting = oxySetting.preSetting
      oxygenSettings.spo2Lower = oxySetting.spo2Lower
      oxygenSettings.spo2LowerDuration = oxySetting.spo2LowerDuration
      oxygenSettings.spo2SevereLower = oxySetting.spo2SevereLower
      oxygenSettings.spo2SevereLowerDuration = oxySetting.spo2SevereLowerDuration
      oxygenSettings.prUpper = oxySetting.prUpper
      oxygenSettings.prLower = oxySetting.prLower
      oxygenSettings.o2PulseRateLower = oxySetting.o2PulseRateLower
      oxygenSettings.o2PulseRateLowerDuration = oxySetting.o2PulseRateLowerDuration
      oxygenSettings.o2PulseRateUpper = oxySetting.o2PulseRateUpper
      oxygenSettings.o2PulseRateUpperDuration = oxySetting.o2PulseRateUpperDuration
      oxygenSettings.startTime = oxySetting.startTime || ''
      oxygenSettings.endTime = oxySetting.endTime || ''
    }
  }
})
</script>
<style scoped lang="scss">
page {
  background: #e3ecf2;
  height: 100%;
}

.picker-view {
  width: 100%;
  height: 300rpx;

  .picker-item {
    line-height: 100rpx;
    text-align: center;
  }
}
</style>