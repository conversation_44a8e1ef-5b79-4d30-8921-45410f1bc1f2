<template>
    <up-popup 
    :show.sync="visible" 
    @close="close" 
    @open="open" 
    :closeOnClickOverlay="closeOnClickOverlay"
    :safeAreaInsetTop="safeAreaInsetTop"
    :round="round"
    :overlayOpacity="overlayOpacity"
    :safeAreaInsetBottom="true"
    :mode="mode"
    :duration="duration">
        <view class="popup-container">
            <view class="popup-title">
                <text>{{ title }}</text>
                <uni-icons v-if="!hideCloseBtn" @click="onButtonClose" custom-prefix="iconfont" type="icon-close" size="16" color="#888"></uni-icons>
            </view>
            <view class="popup-content" :style="{padding: paddingContent}">
                <slot></slot>
            </view>
            <view class="popup-footer">
                <slot name="footer"></slot>
            </view>
            <view class="loading-mark" v-if="loading">
                <uni-icons custom-prefix="iconfont icon-rotating" type="icon-maobiquan_lancopy" size="16" color="#80CAFF"></uni-icons>
            </view>
        </view>
    </up-popup>
</template>
<script>
export default {
    name: "Popup",
    model: {
        prop: 'modelValue',
        event: 'input'
    },
    props: {
        modelValue: {
            type: Boolean,
            default: () => {
                return '';
            }
        },
        loading: false,
        title: {
            default: '标题'
        },
        closeOnClickOverlay: true,
        safeAreaInsetTop: false,
        round: {
            default: 0
        },
        overlayOpacity: {
            default: 0.5
        },
        paddingContent: {
            type: String,
            default: '0 32rpx 0'
        },
        hideCloseBtn: {
            type: Boolean,
            default: false
        },
        mode: {
            default: 'bottom'
        },
        duration: {
            default: 300
        },
    },
    computed: {
        visible: {
            get() {
                return this.modelValue;
            },
            set(val) {
                this.$emit('update:modelValue', val);
            }
        }
    },
    watch: {
        visible(val) {
            if (!val) {
                this.onButtonClose()
            }
        }  
    },
    methods: {
        onButtonClose() {
            this.visible = false
            uni.showTabBar()
            this.$emit('close')
        },
        close() {
            console.log('关闭')
            this.visible = false
            uni.showTabBar()
        },
        open() {
            // console.log('打开')
            uni.hideTabBar()
        }
    },
    mounted() {
    }
}
</script>
<style lang="scss" scoped>
.popup-container{
    position: relative;
    width: 750rpx;
	.popup-title {
        position: relative;
        color: #1A202C;
		font-size: 32rpx;
		text-align: center;
		margin-top: 40rpx;
        font-weight: bold;
		.iconfont{
            display: inline-block;
            padding: 14rpx;
			color: #333333;
			position: absolute;
			right: 32rpx;
            top: -6rpx;
			font-size: 32rpx;
			font-weight: bold;
            z-index: 1;
		}
	}
	.popup-content{
		padding: 0 32rpx 0;
		margin-top: 40rpx;
        min-height: 340rpx;
        color: black;
	}
	.popup-footer{
		display: flex;
		justify-content: space-evenly;
		// padding: 44rpx 0;
        // padding-bottom: 20rpx;
		// border-top: 2rpx solid #eee;
		.button{
			width: 120rpx;
			min-width: 120rpx;
			border-radius: 30px;
			&.close{
				background: #F3F3F5;
				color: #147BD1;
			}
		}
	}
    .loading-mark {
        position: absolute;
        top: calc(50% - 32rpx);
        left: calc(50% - 50px);
        width: 100px;
        text-align: center;
        font-size: 32px;
        
        .iconfont {
            color: #0052A8;
            font-weight: bold;
            font-size: 48rpx;
        }
    }
}
</style>