<template>
  <view class="bluetooth-listener">
    <view class="status-section">
      <text>蓝牙状态: {{ isAvailable ? '可用' : '不可用' }}</text>
      <text>连接状态: {{ isConnected ? '已连接' : '未连接' }}</text>
    </view>
    
    <!-- 最新测量结果 -->
    <view class="measurement-section" v-if="latestMeasurementResult">
      <text class="section-title">最新测量结果</text>
      <view class="data-item">
        <text>收缩压: {{ latestMeasurementResult.systolic }} mmHg</text>
      </view>
      <view class="data-item">
        <text>舒张压: {{ latestMeasurementResult.diastolic }} mmHg</text>
      </view>
      <view class="data-item">
        <text>心率: {{ latestMeasurementResult.heartRate }} bpm</text>
      </view>
      <view class="data-item">
        <text>时间: {{ formatTime(latestMeasurementResult.timestamp) }}</text>
      </view>
    </view>
    
    <!-- 最新袖带压 -->
    <view class="pressure-section" v-if="latestCuffPressure">
      <text class="section-title">最新袖带压</text>
      <view class="data-item">
        <text>压力: {{ latestCuffPressure.pressure }} mmHg</text>
      </view>
      <view class="data-item">
        <text>时间: {{ formatTime(latestCuffPressure.timestamp) }}</text>
      </view>
    </view>
    
    <!-- 电池信息 -->
    <view class="battery-section" v-if="latestBatteryInfo">
      <text class="section-title">电池信息</text>
      <view class="data-item">
        <text>电压: {{ latestBatteryInfo.voltage }} mV</text>
      </view>
      <view class="data-item">
        <text>电量: {{ latestBatteryInfo.percentage }}%</text>
      </view>
    </view>
    
    <!-- 历史数据 -->
    <view class="history-section">
      <text class="section-title">历史数据 (最近10条)</text>
      <view v-for="(item, index) in measurementHistory" :key="index" class="history-item">
        <text>{{ formatTime(item.timestamp) }} - {{ item.systolic }}/{{ item.diastolic }} mmHg</text>
      </view>
    </view>
    
    <!-- 操作按钮 -->
    <view class="action-section">
      <button @click="startMeasurement" :disabled="!isConnected">开始测量</button>
      <button @click="clearHistory">清除历史</button>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, computed } from 'vue';
import { useBlueToothData } from '@/common/BlueTooth';

// 使用蓝牙数据
const {
  // 状态
  state,
  isAvailable,
  isConnected,
  
  // 响应式数据
  latestMeasurementResult,
  latestCuffPressure,
  latestBatteryInfo,
  rawCharacteristicData,
  
  // 回调方法
  onMeasurementResult,
  onCuffPressure,
  onBatteryInfo,
  onRawData,
  clearAllCallbacks,
  
  // 功能方法
  pressure_startMeasurement
} = useBlueToothData();

// 历史数据
const measurementHistory = ref<any[]>([]);

// 取消回调的函数
let unsubscribeMeasurement: (() => void) | null = null;
let unsubscribeCuffPressure: (() => void) | null = null;
let unsubscribeBattery: (() => void) | null = null;
let unsubscribeRawData: (() => void) | null = null;

// 格式化时间
const formatTime = (timestamp: number) => {
  return new Date(timestamp).toLocaleTimeString();
};

// 开始测量
const startMeasurement = async () => {
  try {
    await pressure_startMeasurement();
    uni.showToast({
      title: '开始测量',
      icon: 'success'
    });
  } catch (error) {
    uni.showToast({
      title: '测量失败',
      icon: 'error'
    });
  }
};

// 清除历史
const clearHistory = () => {
  measurementHistory.value = [];
  uni.showToast({
    title: '历史已清除',
    icon: 'success'
  });
};

onMounted(() => {
  // 注册测量结果回调
  unsubscribeMeasurement = onMeasurementResult((result) => {
    console.log('收到测量结果:', result);
    
    // 添加到历史记录
    measurementHistory.value.unshift(result);
    
    // 只保留最近10条
    if (measurementHistory.value.length > 10) {
      measurementHistory.value = measurementHistory.value.slice(0, 10);
    }
    
    // 显示通知
    uni.showToast({
      title: `测量完成: ${result.systolic}/${result.diastolic}`,
      icon: 'success'
    });
  });
  
  // 注册袖带压回调
  unsubscribeCuffPressure = onCuffPressure((pressure) => {
    console.log('收到袖带压数据:', pressure);
  });
  
  // 注册电池信息回调
  unsubscribeBattery = onBatteryInfo((battery) => {
    console.log('收到电池信息:', battery);
  });
  
  // 注册原始数据回调
  unsubscribeRawData = onRawData((data) => {
    console.log('收到原始蓝牙数据:', data);
  });
});

onUnmounted(() => {
  // 取消所有回调
  if (unsubscribeMeasurement) unsubscribeMeasurement();
  if (unsubscribeCuffPressure) unsubscribeCuffPressure();
  if (unsubscribeBattery) unsubscribeBattery();
  if (unsubscribeRawData) unsubscribeRawData();
});
</script>

<style scoped>
.bluetooth-listener {
  padding: 20rpx;
}

.status-section,
.measurement-section,
.pressure-section,
.battery-section,
.history-section,
.action-section {
  margin-bottom: 40rpx;
  padding: 20rpx;
  border: 1px solid #eee;
  border-radius: 10rpx;
  background-color: #fff;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
  color: #333;
}

.data-item {
  padding: 10rpx 0;
  border-bottom: 1px solid #f0f0f0;
}

.data-item:last-child {
  border-bottom: none;
}

.history-item {
  padding: 15rpx;
  margin-bottom: 10rpx;
  background-color: #f8f8f8;
  border-radius: 8rpx;
}

button {
  padding: 20rpx 40rpx;
  margin: 10rpx;
  border: none;
  border-radius: 10rpx;
  background-color: #007aff;
  color: white;
  font-size: 28rpx;
}

button:disabled {
  background-color: #ccc;
}
</style>
