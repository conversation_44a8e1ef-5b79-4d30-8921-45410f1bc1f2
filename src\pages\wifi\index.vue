<template>
	<view class="bg-[#e3ecf2] p-[30rpx]">
		<view class="bg-white rounded-[25rpx] m-[15rpx] p-[40rpx]" v-for="(item, index) in patientsInfoData"
			:key="index">
			<view class="flex items-center mb-2">
				<text class="text-xl">{{ item.patientName }}</text>
				<text class="pl-[20rpx] text-sm">设备编号：{{ item.deviceNo?.slice(-4) }}</text>
				<text class="online pl-[20rpx] flex-1" :class="'online' + item.onlineState">{{  getStatusText(item.onlineState)}}</text>
				<uni-icons type="plus-filled" size="26" :color="getIconColor(item)" @click="goToAdd(item)"></uni-icons>
			</view>
			<up-line color="#2979ff"></up-line>
			<view v-for="(deviceItem, index) in item.homeWifiList" :key="index" @click="goToUpdate(deviceItem,item.patientName)">
				<view class="flex flex-row justify-between items-center py-2">
					<view class="flex flex-col">
						<view class="text-lg">{{ deviceItem.wifiName }}</view>
						<view class="text-sm">{{ deviceItem.wifiPassword }}</view>
					</view>
					<image src="@/static/img/setwifi.png" class="w-[48rpx] h-[48rpx] object-contain"></image>
				</view>
				<up-line color="#999999"></up-line>
			</view>
		</view>
	</view>
</template>

<script setup lang="ts">
import { onMounted, ref,computed } from "vue";
import { onLoad, onShow } from "@dcloudio/uni-app";
import {
	getAppQueryPatientWifi
} from '@/common/api/patientWifi'

import type {
	PatientsInfo,
	Wifi
} from '@/models/patientWifi'

const goToAdd = (item: PatientsInfo) => {
	let paramas: Wifi = {
		wifiId: '',     // Wi-Fi 的 ID，数字类型
		patientId: item.patientId,   // 患者 ID，数字类型
		patientName: item.patientName,   // 患者 姓名，数字类型
		wifiName: "",     // Wi-Fi 名称，字符串类型
		wifiPassword: ""
	}
	uni.navigateTo({
		url: `/pages/wifi/addEdit?paramas=${encodeURIComponent(JSON.stringify(paramas))}`,
		animationType: 'slide-in-right',
		animationDuration: 300,
	})

}

const goToUpdate = (item: Wifi, patientName: string) => {
console.log('wifi修改')
	const data = { ...item, patientName }
	console.log(data)
	uni.navigateTo({
		url: `/pages/wifi/addEdit?paramas=${encodeURIComponent(JSON.stringify(data))}`,
		animationType: 'slide-in-right',
		animationDuration: 300,
	})

}

const patientsInfoData = ref<PatientsInfo[]>([])
const requestHomeWifi = () => {
	let bodyParams = {
		"condition": {
			"patientName": ""
		},
		"pageNum": 1,
		"pageSize": 100,
		"sort": ""
	}
	getAppQueryPatientWifi(bodyParams).then((res) => {
		console.log("PatientWifi~~~" + JSON.stringify(res.data.list));
		patientsInfoData.value = res.data.list;
	}).catch((error) => {
		console.log(error);
		uni.showToast({
			title: '数据拉取失败',
			icon: 'none'
		})
	})
}
// 设备状态 0 关机 1在线 2休眠 3断网
const onlineArr = ['关机', '在线', '休眠', '断网']
const getIconColor=(item:any)=> {
      return item.onlineState === 1 ? "#00FF00" : "#999"; // 在线为绿色，离线为灰色
    }
const getStatusText=(state:any)=> {
      // 如果 state 在 0 到 3 范围内，返回对应的状态文字，否则返回未知
	  console.log(' state~~~~'+ state)
	  console.log(' onlineArr[state]~~~~'+ onlineArr[state])

	  return onlineArr[state] || '未知状态';
    }
onLoad(() => {
	console.log('TREND【onLoad】：页面加载完成')
	requestHomeWifi();
})

onShow(() => {
	console.log("TREND【onShow】：页面重新可见");

	//检查token情况
	requestHomeWifi();
});

</script>

<style scoped lang="scss">
:deep(.is-input-border) {
	border: none;
	border-bottom: 1px solid #dcdfe6;
}
.online {
    font-size: 28rpx;

    &::before {
        position: relative;
        top: 4rpx;
        margin-right: 8rpx;
        content: "";
        display: inline-block;
        width: 30rpx;
        height: 30rpx;
        border-radius: 50%;
        background: #999999;
    }

    &.online1 {
        &::before {
            background: #02BB00;
        }
    }

    &.online2 {
        &::before {
            background: #d1bf30;
        }
    }
}
</style>