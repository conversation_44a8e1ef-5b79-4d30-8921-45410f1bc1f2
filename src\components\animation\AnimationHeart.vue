<template>
    <view class="heart-rate-monitor">
      <!-- 黄色动态格子 -->
      <view class="bars">
        <view
          v-for="(bar, index) in totalBars"
          :key="index"
          class="bar"
          :class="{ active: index < activeBars }"
        ></view>
      </view>
    </view>
  </template>
  
  <script setup>
  import { ref, watch } from 'vue';

  const props = defineProps({
    animationHr: {
        type: Number,
        default: 0
    }
  })
  
  const totalBars = 10; // 总格子数量
  const activeBars = ref(0); // 当前激活的格子数量


  let direction = 1; // 增加方向
  let timer = null;
  const autoAnimation = () => {
    // 动态更新心率格子的数量
    clearInterval(timer);

    // 如果没有心率，就不在跳动
    if (props.animationHr === 0) {
        activeBars.value = 0
        return
    }

    timer = setInterval(() => {
        if (activeBars.value >= totalBars) {
            direction = -1; // 达到最大格子后，开始回落
        } else if (activeBars.value <= 0) {
            direction = 1; // 回落到0后，重新开始增加
        }
        // 动态更新心率格子的数量
        activeBars.value += direction;
    }, (60 / props.animationHr) * 50); // 心率变化的速度
  }

    // 监听animationHr 变化
    watch(() => props.animationHr, () => {
        autoAnimation();
    }, { immediate: true });

  </script>
  
  <style lang="scss" scoped>
  .heart-rate-monitor {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin: 0 20rpx;
  }
  .bars {
    display: flex;
    flex-direction: column-reverse;
    justify-content: flex-end;
    height: 200rpx;
    width: 24rpx;
  }
  
  .bar {
    width: 100%;
    height: 14rpx; /* 每个格子高度 */
    margin: 6rpx 0 0;
    transition: background-color 0.1s ease;
  }
  
  .bar.active {
    background-color: #666; /* 激活的格子变黄 */
  }
</style>