<template>
    <view class="w-full h-full">
        <scroll-view scroll-y class=" h-full">
            <view class="flex flex-wrap justify-between m-30rpx">
                <view v-for="(item, index) in dataList" :key="index" class="w-45% h-280rpx mb-24rpx p-14rpx bg-white rounded-24rpx i-shadow" @click.stop="onClickVideo(item)">
                    <view class="flex justify-center items-center h-164rpx bg-[##d1d1d1] rounded-8rpx">
                        <image class="w-96rpx absolute" :src="item.img" mode="widthFix" />
                        <view class="bg-[#202B35] rounded-50% border-[4rpx_solid_white] p-16rpx z-1 opacity-50">
                            <u-icon size="18" name="play-right-fill" color="#ffffff"></u-icon>
                        </view>
                    </view>
                    <view class="flex justify-center items-center h-78rpx text-28rpx">{{ item.title }}</view>
                </view>
            </view>
        </scroll-view>

        <view v-if="isPlayVideo">
            <video autoplay id="my-video" :src="videoUrl" @fullscreenchange="screenChange"></video>
        </view>
    </view>
</template>

<script setup lang="ts">
import { reactive, ref, getCurrentInstance } from "vue";
import environment from "@/common/environments"; // 环境，服务配置文件

const baseUrl = environment.envConfigs.url + "/need/static/video/";
// const baseUrl = "https://need.service.i-need.com.cn:39002/need/static/video/";

const instance = getCurrentInstance();

const dataList = reactive([
    {
        title: "动态无线电子体温计",
        img: "/static/products/thermometer01.png",
    },
    {
        title: "连续测量电子体温计",
        img: "/static/products/thermometer02.png",
    },
    {
        title: "血压血氧一体机",
        img: "/static/products/blood-pressure01.png",
    },
    {
        title: "远程动态血压监测系统",
        img: "/static/products/blood-pressure02.png",
    },
    {
        title: "无线动态医用脉搏血氧仪",
        img: "/static/products/bool-oxygen01.png",
    },
]);

const isPlayVideo = ref<boolean>(false);

const videoUrl = ref<string>("");

const videoContext = ref<any>();

function screenChange(e: any) {
    let fullScreen = e.detail.fullScreen; //值true为进入全屏，false为退出全屏
    if (!fullScreen) {
        //退出全屏
        isPlayVideo.value = false; // 隐藏播放盒子
    }
}

function onClickVideo(item: any) {
    isPlayVideo.value = true; // 显示播放盒子
    console.log(instance);
    videoContext.value = uni.createVideoContext("my-video", instance);
    console.log(videoContext.value)
    setTimeout(() => {
        videoUrl.value = `${baseUrl}${item.title}.mp4`;
        console.log(videoUrl.value);
        videoContext.value.requestFullScreen({ direction: 90 }); //direction: 90  控制全屏的时候视屏旋转多少度
        videoContext.value.play();
    }, 200);
}
</script>
<style lang="scss" scoped>
.i-shadow {
    box-shadow: 0px 4rpx 8rpx rgba(42, 130, 228, 0.1);
}
</style>