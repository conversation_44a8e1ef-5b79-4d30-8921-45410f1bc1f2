<template>
    <view class="data-item">
        <view class="text-[#1198F3]">
            <text class="text-[72rpx] w-[270rpx] inline-block text-right pr-[10rpx]">{{ item.systolic }}/{{ item.diastolic }}</text>
            <text class="unit">高压/低压(mmHg)</text>
        </view>
        <view class="flex items-end">
            <view class="text-[#1198F3]">
                <text class="text-[40rpx] w-[270rpx] inline-block text-right pr-[10rpx] font-700">{{ item.heartRate }}</text>
                <text class="unit">脉搏(bpm)</text>
            </view>
            <view class="flex flex-col items-end flex-1 text-[24rpx]">
                <text class="font-500 pb-[4rpx]">{{ moment(item.measureTime).format('HH:mm') }}</text>
                <text>{{ moment(item.measureTime).format('YYYY-MM-DD') }}</text>
            </view>
        </view>
<!-- 
        <view class="bp-level" :style="{ color: levelColor }">
            {{ bloodPressureLevel }}
        </view> -->

        <view class="bg-[#1198F3] h-[0.5px] mt-[16rpx]"></view>
    </view>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { getErrorCodeInfo } from '@/utils/errorCodeHandler';
import { deleteMeasurementData, type MeasurementData } from '@/utils/measurementCache';
import moment from "moment";

const props = defineProps<{
    item: MeasurementData;
}>();

const emit = defineEmits<{
    refresh: [];
}>();

// 血压等级计算
const bloodPressureLevel = computed(() => {
    const { systolic, diastolic } = props.item;
    
    if (systolic < 90 || diastolic < 60) {
        return '低血压';
    } else if (systolic < 120 && diastolic < 80) {
        return '正常';
    } else if (systolic < 130 && diastolic < 80) {
        return '正常高值';
    } else if (systolic < 140 || diastolic < 90) {
        return '1级高血压';
    } else if (systolic < 160 || diastolic < 100) {
        return '2级高血压';
    } else {
        return '3级高血压';
    }
});

// 血压等级颜色
const levelColor = computed(() => {
    const level = bloodPressureLevel.value;
    
    if (level === '正常') {
        return '#52c41a';
    } else if (level === '正常高值') {
        return '#faad14';
    } else if (level.includes('高血压')) {
        return '#ff4d4f';
    } else if (level === '低血压') {
        return '#1890ff';
    } else {
        return '#666666';
    }
});


</script>

<style scoped lang="scss">

.unit {
    font-size: 24rpx;
}

</style>
