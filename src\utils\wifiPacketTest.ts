// WiFi数据包测试工具
export function testWifiPacket(ssid: string, password: string) {
    console.log('=== WiFi数据包测试 ===');
    console.log('SSID:', ssid);
    console.log('Password:', password);
    
    // 构建数据包
    const ssidBytes = new TextEncoder().encode(ssid);
    const passwordBytes = new TextEncoder().encode(password);
    
    // 计算数据长度
    const dataLength = ssidBytes.length + 1 + passwordBytes.length + 1;
    
    // 创建数据缓冲区
    const dataBuffer = new ArrayBuffer(dataLength);
    const dataView = new Uint8Array(dataBuffer);
    
    let offset = 0;
    
    // 写入SSID
    dataView.set(ssidBytes, offset);
    offset += ssidBytes.length;
    dataView[offset++] = 0; // null terminator
    
    // 写入密码
    dataView.set(passwordBytes, offset);
    offset += passwordBytes.length;
    dataView[offset++] = 0; // null terminator
    
    // 构建完整数据包
    // 包头(0x5B) + 长度 + 包类型(0x08) + 数据 + 包尾(0xB5)
    const packetLength = dataLength;
    const fullPacketLength = 1 + 1 + 1 + dataLength + 1; // 包头 + 长度 + 包类型 + 数据 + 包尾
    
    const fullPacket = new Uint8Array(fullPacketLength);
    let packetOffset = 0;
    
    // 包头
    fullPacket[packetOffset++] = 0x5B;
    
    // 长度
    fullPacket[packetOffset++] = packetLength;
    
    // 包类型
    fullPacket[packetOffset++] = 0x08;
    
    // 数据
    fullPacket.set(dataView, packetOffset);
    packetOffset += dataLength;
    
    // 包尾
    fullPacket[packetOffset++] = 0xB5;
    
    // 输出结果
    const hexString = Array.from(fullPacket)
        .map(b => b.toString(16).padStart(2, '0').toUpperCase())
        .join('');
    
    console.log('数据长度:', dataLength);
    console.log('完整数据包长度:', fullPacketLength);
    console.log('数据包(十六进制):', hexString);
    console.log('数据包(字节数组):', Array.from(fullPacket));
    
    // 验证示例
    if (ssid === 'GXYS' && password === '12345678') {
        const expected = '5B0D084758595300313233343536373800B5';
        console.log('期望结果:', expected);
        console.log('实际结果:', hexString);
        console.log('匹配结果:', hexString === expected ? '✅ 匹配' : '❌ 不匹配');
    }
    
    return {
        dataLength,
        fullPacketLength,
        hexString,
        byteArray: Array.from(fullPacket)
    };
}

// 测试函数
export function runWifiPacketTests() {
    console.log('开始WiFi数据包测试...');
    
    // 测试示例1：文档中的示例
    testWifiPacket('GXYS', '12345678');
    
    console.log('\n');
    
    // 测试示例2：其他WiFi
    testWifiPacket('MyWiFi', 'password123');
    
    console.log('\n');
    
    // 测试示例3：中文WiFi名称
    testWifiPacket('我的WiFi', 'mypassword');
}
