<template>
    <view>
        <view v-if="showDate" class="flex justify-between items-center">
            <view>
                <text>{{ moment(props.item?.measureTime).format('YYYY-MM-DD HH:mm') }}</text>
                <text v-if="props.item.valid != 1" class="tag-fail">{{ validText() }}</text>
            </view>
            <text class="text-[26rpx]" :style="{color: bpLevelColor}">{{ bpLevelName }}</text>
        </view>
        <view v-if="showDate" class="h-[1px] w-full bg-[#DDDDDD] mt-[30rpx] mb-[30rpx]"></view>
        <view class="flex">
            <view class="flex flex-col items-center flex-1">
                <text class="text-[56rpx] pt-[20rpx] pb-[20rpx]">{{ props.item.sbp || '-' }}</text>
                <text class="text-[28rpx] color-[#333333]">收缩压</text>
            </view>
            <view class="flex flex-col items-center flex-1">
                <text class="text-[56rpx] pt-[20rpx] pb-[20rpx]">{{ props.item.dbp || '-' }}</text>
                <text class="text-[28rpx] color-[#333333]">舒张压</text>
            </view>
            <view class="flex flex-col items-center flex-1">
                <text class="text-[56rpx] pt-[20rpx] pb-[20rpx]">{{ props.item.pulse || '-' }}</text>
                <text class="text-[28rpx] color-[#333333]">脉搏</text>
            </view>
            <view v-if="props.arm" class="flex flex-col items-center flex-1">
                <view class=" flex-1 flex items-center">
                    <text class="text-[56rpx]">{{ armText }}</text>
                </view>
                <text class="text-[28rpx] color-[#333333]">手臂</text>
            </view>
            <view v-else class="flex flex-col items-center flex-1">
                <view class=" flex-1 flex items-center">
                    <text v-if="!posture" class="text-[56rpx]">-</text>
                    <uni-icons v-else custom-prefix="iconfont" :type="posture"size="38"></uni-icons>
                </view>
                <text class="text-[28rpx] color-[#333333]">姿态</text>
            </view>
        </view>
    </view>
</template>
<script setup lang="ts">
import { ref, reactive, onMounted, onBeforeUnmount, computed, nextTick } from 'vue';
import moment from 'moment';
import { useUserInfo } from '@/stores/userInfo';
const storeUserInfo = useUserInfo();


function getSphyStatus(sbp: number, dpb: number, type: 'name' | 'color' = 'color'): string {
  // 输入校验
  if (typeof sbp !== 'number' || typeof dpb !== 'number' || sbp < 0 || dpb < 0) {
      return { name: '未知', color: '#C1C1C1' }[type];
  }

  // 定义阈值和对应的状态
  const thresholds = [
      { maxSbp: 90, maxDbp: 60, value: { name: '低压', color: '#DC3C3D' } },
      { maxSbp: 120, maxDbp: 80, value: { name: '正常', color: '#1aba62' } },
      { maxSbp: 140, maxDbp: 90, value: { name: '正常偏高', color: '#1aba62' } },
      { maxSbp: 160, maxDbp: 100, value: { name: '轻度', color: '#FD9902' } },
      { maxSbp: 180, maxDbp: 110, value: { name: '中度', color: '#FA7507' } },
      { maxSbp: Infinity, maxDbp: Infinity, value: { name: '重度', color: '#DC3C3D' } },
  ];

  // 查找匹配的阈值
  for (const { maxSbp, maxDbp, value } of thresholds) {
      if (sbp <= maxSbp && dpb <= maxDbp) {
          return value[type];
      }
  }

  // 默认返回值（理论上不会到达此处）
  return { name: '未知', color: '#C1C1C1' }[type];
}

const props = defineProps({
    item: {
        type: Object,
        default: () => {},
    },
    showDate: {
        type: Boolean,
        default: true
    },
    arm: {
        type: Boolean,
        default: false
    }
})


const bpLevelName = computed(() => {
    return getSphyStatus(props.item.sbp, props.item.dbp, 'name')
})

const bpLevelColor = computed(() => {
    return getSphyStatus(props.item.sbp, props.item.dbp, 'color')
})

const armText = computed(() => {
    const obj = storeUserInfo.dictCodeInfo.arm?.find((item: any) => item.itemValue == props.item.arm)

    if (obj) {
        return obj.itemName.slice(0, 1)
    }

    return '-'
})

const validText = () => {
    const obj = storeUserInfo.dictCodeInfo.valid?.find((item: any) => item.itemValue == props.item.valid)

    if (obj) {
        return obj.itemName
    }
    return '-'
}

const posture = computed(() => {
    const p = props.item.posture
    if (p === undefined || p === null) {
        return ''
    }

    const icons = ['icon-zuozhuoderen', 'icon-S_HOTERU']

    return icons[p] || ''

})
</script>
<style lang="scss" scoped>
.tag-fail {
    margin-left: 10rpx;
    color: #fff;
    background-color: #dc3c3d;
    border-radius: 8rpx;
    padding: 4rpx 12rpx;
    font-size: 24rpx;
}
</style>