<template>
    <view class="flex flex-col w-full h-full bg-[#F7F9FC]">
        <view class="item-01 flex items-center">
            <u-tabs :list="tabsItem" :activeStyle="{
                color: '#014777',
                fontSize:'32rpx',
                transform: 'scale(1.05)'
            }" :inactiveStyle="{
                color: '#333',
                fontSize:'30rpx',
                transform: 'scale(1)'
            }" @click="click"></u-tabs>
        </view>
        <u-collapse :border="false" :value="tabsList.list?.map(item => item.months)">
            <u-collapse-item v-for="(item, index) in tabsList.list" :key="index" :border="false" :title="item.months" :name="item.months">
                <u-swipe-action v-for="innerItem in item.list" :key="innerItem.id">
                    <u-swipe-action-item :options="options1">
                        <view>
                            <view class="p-15rpx" @click="onClickItem(innerItem)">
                                <view class="left flex flex-col">
                                    <text class="text-lg">{{ innerItem.title }}</text>
                                    <text class="mt-15rpx">{{ innerItem.date }}</text>
                                </view>
                                <view class="py-15rpx flex" v-if="innerItem.type != '影像报告'">
                                    <view class="ml-15rpx py-10rpx px-30rpx [border:1rpx_solid_#005bea] [border-radius:50rpx]">查看</view>
                                </view>
                            </view>
                            <view class="pb-15rpx flex" v-if="innerItem.type == '影像报告'">
                                <view class="ml-30rpx py-10rpx px-30rpx [border:1rpx_solid_#005bea] [border-radius:50rpx]" @click="onClickItem(innerItem, 'type1')">报告</view>
                                <view class="ml-30rpx py-10rpx px-30rpx [border:1rpx_solid_#005bea] [border-radius:50rpx]" @click="onClickItem(innerItem)">影像</view>
                            </view>
                        </view>
                    </u-swipe-action-item>
                </u-swipe-action>
            </u-collapse-item>
        </u-collapse>
        <u-empty v-if="!tabsList.list?.length" mode="list" text="无列表内容"></u-empty>
        <!-- <swiper class="container-tabs__swiper" :current="current" @change="onAnimationChange">
            <swiper-item class="swiper-item" v-for="(outItem, outIndex) in tabsList" :key="outIndex">
                <scroll-view 
                scroll-y 
                style="height: 100%"
                :refresher-enabled="true"
                refresher-background="#F7F9FC"
                :refresher-triggered="refreshTrigger"
                @refresherrefresh="onRefresh">


                
                </scroll-view>
            </swiper-item>
        </swiper> -->
        <!-- <xe-upload ref="XeUpload" :options="uploadOptions" @callback="handleUploadCallback"></xe-upload> -->
        <!-- <view class="addbtn" @click="handleUploadClick"><text class="iconfont icon-jiahao_o"></text></view> -->

        <!-- <u-popup :show="show" mode="center" :closeable="true" :closeOnClickOverlay="false" @close="close">
            <view class="popup">
                <text class="title">上传内容</text>
                <view class="list">

                </view>
                <view class="form">
                    
                </view>
            </view>
        </u-popup> -->
    </view>
</template>
<script>
import environment from '@/common/environments' // 环境，服务配置文件
export default {
    data () {
        return {
            show: false,
            current: '体检报告',
            options1: [{
                text: '删除',
                style: {
                    backgroundColor: '#f56c6c'
                }
            }],
            tabsItem: [{ name: '体检报告' }, { name: '检验报告' }, { name: '影像报告' }, { name: '门诊病历' }], // 项目
            dataList: [
                {
                    type: '体检报告',
                    date: '2023-09-26 11:30',
                    title: '健康体检报告',
                    fileType: 'pdf',
                    url: '/need/static/pdf/report1.pdf',
                    id: 2,
                },
                {
                    type: '影像报告',
                    date: '2024-04-02 10:30',
                    title: 'PT/MR全身影像',
                    subTitle: '图像',
                    url: '49162d488ff945718a5de236e8a0f03b',
                    fileType: 'dicom',
                    id: 5,
                    url1: '/need/static/pdf/mr_head.pdf',
                    fileType1: 'pdf',
                },
                {
                    type: '影像报告',
                    date: '2024-05-10 10:02',
                    title: 'SPET/CT影像',
                    subTitle: '图像',
                    url: '4b34b004f0644e7ca88989e1ee16bf1e',
                    fileType: 'dicom',
                    id: 7,
                    url1: '/need/static/pdf/mr_head.pdf',
                    fileType1: 'pdf',
                },
                {
                    type: '门诊病历',
                    date: '2023-09-26 11:30',
                    title: '第一附属医院病历',
                    fileType: 'jpg',
                    url: '/need/static/img/report/bl1.pdf',
                    id: 2,
                },
            ],

            refreshTrigger: false, // 下拉刷新触发
            uploadOptions: {

            },
            previewUrl: '',
        }
    },
    computed: {
        combinedData () {
            const groupedData = this.dataList.reduce((acc, item) => {
                // 查找类型
                const existingGroup = acc.find(group => group.type === item.type);
                if (existingGroup) {
                    // 查找月份
                    const existingMonth = existingGroup.list.find(month => month.months === item.date.substring(0, 7));
                    if (existingMonth) {
                        // 填入月份
                        existingMonth.list.push(item);
                    } else {
                        // 第一次添加月份
                        existingGroup.list.push({ months: item.date.substring(0, 7), list: [item] });
                    }
                } else {
                    // 第一次添加类型
                    acc.push({ type: item.type, list: [{ months: item.date.substring(0, 7), list: [item] }] });
                }
                return acc;
            }, []);

            return groupedData
        },
        tabsList () {
            const list = {
                '体检报告': this.combinedData.find(item => item.type === '体检报告') || { list: [] },
                '检验报告': this.combinedData.find(item => item.type === '检验报告') || { list: [] },
                '影像报告': this.combinedData.find(item => item.type === '影像报告') || { list: [] },
                '门诊病历': this.combinedData.find(item => item.type === '门诊病历') || { list: [] }
            }

            return list[this.current]
        }
    },
    methods: {
        // 定义方法  
        click (item) {
            this.current = item.name;
        },
        close () {
            this.show = false
        },
        onClickItem (item, type) {
            let url = item.url
            let fileType = item.fileType

            if (type === 'type1') {
                url = item.url1
                fileType = item.fileType1
            }

            if (fileType == 'dicom') {
                uni.navigateTo({
                    url: "/pageUseInfo/appdicom?id=" + url
                });
                return
            }
            if (fileType == 'jpg') {
                this.previewUrl = environment.envConfigs.url + url
                return
            }
            uni.showLoading({
                title: '加载中...',
                mask: true
            })
            uni.downloadFile({
                url: environment.envConfigs.url + url,
                success: function (res) {
                    var filePath = res.tempFilePath;
                    uni.openDocument({
                        filePath: filePath,
                        showMenu: true,
                        fail: (err) => {
                            uni.showToast({ title: '打开失败！', icon: 'error' })

                            setTimeout(() => {
                                uni.showToast({ title: err, icon: 'none' })
                            }, 2000);
                        }
                    });
                },
                complete: () => {
                    uni.hideLoading()
                }
            });
        }
    },
    mounted () {
        console.log(this.combinedData)
    }
}
</script>
<style scoped lang="scss">
.container-tabs__swiper {
    height: 100%;
}
.addbtn {
    position: absolute;
    z-index: 3;
    bottom: 90px;
    right: 32rpx;
    width: 42 * 2rpx;
    height: 42 * 2rpx;
    border-radius: 42rpx;
    background: #00dca7;
    background-size: contain;
    text-align: center;
    line-height: 84rpx;
    .iconfont {
        font-size: 58rpx;
        color: white;
        font-weight: bold;
    }
}
.popup {
    width: 100vw;
    height: 100vh;
    .title {
        display: block;
        text-align: center;
        width: 100%;
        height: 80rpx;
        line-height: 80rpx;
    }
}
</style>