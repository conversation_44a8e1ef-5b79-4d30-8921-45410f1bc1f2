import { bindDevice } from '@/common/api/device'
import { useUserInfo } from '@/stores/userInfo'
function addScanCodeDevice(url, callBack) {
    const storeUserInfo = useUserInfo();
    const getUrlParams = (url) => {
        let resultObj = {};
        let searchUrl = url.split('?', 2)[1];
        if (searchUrl && searchUrl.length > 1) {
            let search = searchUrl;
            let items = search.split('&');
            for (let index = 0; index < items.length; index++) {
                if (!items[index]) {
                    continue;
                }
                let kv = items[index].split('=');
                resultObj[kv[0]] = typeof kv[1] === "undefined" ? "" : kv[1];
            }
        }
        return resultObj;
    }
    const params = getUrlParams(url)
    if (params.deviceNo) {
        bindDevice({ deviceNo: params.deviceNo, userId: storeUserInfo.userId }).then(res => {
            if (res.code === 0) {
                uni.showToast({
                    title: '绑定成功！',
                    icon: 'success',
                    duration: 1000
                })
                setTimeout(() => {
                    callBack && callBack(params.deviceNo)
                }, 200);
                return
            }
            uni.showToast({
                title: res.msg,
                icon: 'none',
                duration: 2500
            });
            
        }).catch((res) => {
            uni.showToast({
                title: res,
                icon: 'none',
                duration: 2500
            });
        })
    }
}

export default function (callBack) {
    // 扫一扫
    // 允许从相机和相册扫码
    uni.scanCode({
        scanType: ["qrCode"],
        success: (res) => {
            if (res.result) {
                addScanCodeDevice(res.result, callBack)
            } else {
                uni.showToast({
                    title: '无法识别',
                    icon: 'error',
                    duration: 1000
                });
                return false;
            }
        },
        fail: () => {
            console.log('未识别到二维码');
        }
    })
}