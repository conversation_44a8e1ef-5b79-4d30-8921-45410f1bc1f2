<template>
	<view>
		<web-view :webview-styles="webviewStyles" :src="baseUrl"></web-view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
                baseUrl: '',
				webviewStyles: {
					progress: {
						color: '#FF3333'
					}
				}
			}
		},
        onLoad(options) {
            console.log(options)
           this.baseUrl = 'http://*************:19102/app/appdicom/#/?sPatientId=' + options.id
        },
	}
</script>

<style>

</style>