<template>
    <view class="mb-[20rpx]">
        <view style="background-color: #E3ECF2;position: sticky;z-index: 2;top: 0px;">
            <view class="text-[34rpx] py-[30rpx] flex justify-between items-center">
                <text style="text-shadow: 3px 3px 3px #b9b9b9;">设置计划</text>
                <uni-icons v-if="props.steps >= 40 && dataList.length" custom-prefix="iconfont" type="icon-dagou"size="28" color="#1ABA62"></uni-icons>
            </view>
        </view>
        <view v-if="dataList.length && props.steps >= 40" class="py-[20rpx] px-[30rpx] bg-white text-[32rpx] rounded-[16rpx]">
            <view v-for="item in dataList" class="flex justify-between items-center py-[20rpx]">
                <text>{{ item.timeFrameName || '--' }}</text>
                <text>{{ item.startTime }}-{{ item.endTime }}</text>
                <text class="w-[200rpx] text-right">每隔{{ item.interval }}分钟</text>
            </view>
            <view class="flex justify-center mt-[30rpx]">
                
                <view v-if="props.steps === 40" @click="goTo" class="w-160rpx text-center text-[28rpx] color-[#1296DB] py-[16rpx] rounded-[8rpx]" style="border: 1px dashed #d3d3d3;">重设</view>
                <!-- <view class="w-[40rpx]"></view> -->
                <!-- <view v-if="dataList.length && props.steps === 40" @click="onNext" class="w-160rpx text-center text-[28rpx] color-[#1296DB] py-[16rpx] rounded-[8rpx]" style="border: 1px dashed #d3d3d3;">下一步</view> -->
            </view>
        </view>
        <view v-else class="p-[30rpx] pt-[60rpx] pb-[40rpx] bg-white text-[32rpx] rounded-[16rpx]">
            <up-empty mode="list" text="暂无计划" icon="/static/img/empty1.png" height="83" width="145"></up-empty>
            <view @click="goTo" class="m-auto w-200rpx text-center bg-[#1296DB] text-[28rpx] mt-[20rpx] color-white py-[16rpx] rounded-[8rpx]">去设置</view>
        </view>
    </view>
</template>
<script setup lang="ts">
import { ref, reactive, onMounted, onBeforeUnmount, computed, nextTick } from 'vue';
import { onLoad, onShow, onUnload } from "@dcloudio/uni-app";
import { getPatientBindingDevice, updateMeasureStatus } from '@/common/api/task';
const props = defineProps({
    patient: {
        type: Object,
        default: () => {},
    },
    device: {
        type: Object,
        default: () => {},
    },
    steps: {
        type: Number,
        default: 0
    }
})

const dataList: any = ref([])

const goTo = () => {
    uni.navigateTo({
        url: '/pages/home/<USER>' + props.patient.patientId,
        animationType: "slide-in-right",
        animationDuration: 300,
    })
}

const onNext = () => {
    updateMeasureStatus({ measureStatus: 40, patientId: props.patient.patientId }).then(() => {
        props.patient.executeStatus = 40
    })
}

onShow(() => {
    getPatientBindingDevice({patientId: props.patient.patientId}).then(res => {
        const list: any = []
        if (res.data.planJson) {
          // 更新计划信息
          props.device.planJson = res.data.planJson
          const arrObj = JSON.parse(res.data.planJson)
          console.log(arrObj)

          arrObj.s0081List?.forEach((item: any) => {
            list.push({
              timeFrameName: item.timeFrameName,
              startTime: item.startTime,
              endTime: item.endTime,
              interval: item.interval
            })
          })
          arrObj.s0082List?.forEach((item: any) => {
            list.push({
              timeFrameName: '血氧',
              startTime: item.startTime,
              endTime: item.endTime,
              interval: item.interval
            })
          })
        }
        dataList.value = list

    }).catch(() => {
        // isLoadDevice.value = false
    })
})
</script>