<template>
	<view v-if="props.show" style="width: 100%;">
		<!-- 弹窗蒙版 -->
		<view class="mask" catchtouchmove="preventTouchMove"></view>

		<view class="other_popup" :style="'bottom:' + (props.show ? '0px' : '')">

			<view class="flex items-center px-[28rpx] py-[36rpx]">
				<view class="" @click="onCancel">取消</view>
				<view class="flex flex-row flex-1 justify-center px-[36rpx] text-lg font-bold">
					请选择
				</view>

				<view class="text-blue" @click="onConfirm">确定</view>
			</view>

			<picker-view class="picker-view" indicator-style="height: 50px;" :value="pickerIndexes" @change="onChange">
				<picker-view-column>
					<view style="line-height: 50px;" :style="{textAlign: dataType== 'integer' ? 'center':'right'}"
						v-for="(item, index) in integerOptions" :key="index">
						{{ item }}
					</view>
				</picker-view-column>

				<picker-view-column v-if="props.dataType !== 'integer'">
					<view style="line-height: 50px; text-align: center;" v-for="(item1, index) in ['.']" :key="index">
						{{ item1 }}
					</view>
				</picker-view-column>

				<picker-view-column v-if="props.dataType !== 'integer'">
					<view style="line-height: 50px; text-align: left;" v-for="(item2, index) in decimalOptions" :key="index">
						{{ item2 }}
					</view>
				</picker-view-column>

			</picker-view>
		</view>
	</view>
</template>

<script setup lang="ts">
	import type { split } from "postcss/lib/list";
	import { onLoad, onShow, onReady } from "@dcloudio/uni-app";
	import { nextTick } from "vue";
	import { ref, reactive, computed, watch } from 'vue';

	const myDefVal = ref();
	const emits = defineEmits(["onCancel", "onConfirm"]);
	const props = withDefaults(
		defineProps<{
			show : boolean;
			dataType : string;
			range : [number, number]; // 定义range属性，类型为[number, number]
			defValue : number
		}>(),
		{
			//没有默认值就是必填项
			show: false,
			//dataType: 'float2', // 默认数据类型为两位小数
			//range: () => [0, 100], // 默认数值区间为[0, 100]
		}
	);

	//// 响应式变量
	const integerOptions = ref<string[]>([]);
	const decimalOptions = ref<string[]>([]);
	const decimalFullOptions = ref<string[]>([]);
	//最小整数对应的小数数组，当整数选择到最小值是这个数组生效
	const decimalMinOptions = ref<string[]>([]);
	//最大整数对应的小数数组，当整数选择到最大值是这个数组生效
	const decimalMaxOptions = ref<string[]>([]);

	const pickerIndexes = ref<number[]>([]);

	// picker-view改变时的处理函数
	const onChange = (e) => {
		console.log(e);
		//值是数据列的索引
		pickerIndexes.value = e.detail.value;

    //选择了整数的第一个
		/* if (e.detail.value[0] == 0) {
			decimalOptions.value = decimalMinOptions.value;
		}else if (e.detail.value[0] == (integerOptions.value.length - 1)) {
			 //选择了整数的最后一个
			 decimalOptions.value = decimalMaxOptions.value;
		}else{
			decimalOptions.value = decimalFullOptions.value;
		} */
	};

	const onConfirm = () => {
		if (!pickerIndexes.value || pickerIndexes.value.length == 0) {
			uni.showToast({
				title: "请选择有效的数值",
				icon: "none"
			})
			return
		}

		let result : string = "";
		let integerVal = integerOptions.value[pickerIndexes.value[0]];
		if (props.dataType == 'integer') {
			result = integerVal;
		} else {
			let decimalVal = decimalOptions.value[pickerIndexes.value[2]];

			console.log("小数部分", decimalVal);
			console.log("=========", integerVal + "." + decimalVal);
			result = integerVal + "." + decimalVal;
			console.log("result", result);
		}
		emits("onConfirm", result)

	};
	const onCancel = (e : any) => {
		console.log(e);
		emits("onCancel")
	};

	function splitNumber(num : number, decimalPlaces : number) {
		// 将数字转换为字符串
		let strNum = num.toString();
		// 找到小数点的位置
		let dotIndex = strNum.indexOf('.');

		// 如果没有小数点，说明是整数
		if (dotIndex === -1) {
			strNum += '.';
			dotIndex = strNum.length - 1;
		}

		// 获取整数部分
		let integerPart = strNum.substring(0, dotIndex);
		// 获取小数部分
		let decimalPart = strNum.substring(dotIndex + 1);

		// 根据需要的小数位数补零
		while (decimalPart.length < decimalPlaces) {
			decimalPart += '0';
		}

		// 如果小数位数超过需要的位数，截取所需位数
		if (decimalPart.length > decimalPlaces) {
			decimalPart = decimalPart.substring(0, decimalPlaces);
		}

		return [integerPart, decimalPart];
	}

	// 监听props的变化并生成选项
	watch(
		() => [props.dataType, props.range],
		() => {
			
			setTimeout(()=> {
				myDefVal.value = props.defValue || props.range[0];
				
				if (props.defValue < props.range[0] || props.defValue > props.range[1]) {
					nextTick(() => {
						myDefVal.value = props.range[0];
					});
				}
				
				const [start, end] = props.range;
				
				integerOptions.value = [];
				decimalOptions.value = [];
				
				//将myDefVal差分为整数和小数两部分
				let decimalPlaces = 0;
				switch (props.dataType) {
					case "integer":
						decimalPlaces = 0;
						break;
					case "float1":
						decimalPlaces = 1;
						break;
					case "float2":
						decimalPlaces = 2;
						break;
					default:
						decimalPlaces = 0;
						break;
				}
				
				let minParts = splitNumber(props.range[0], decimalPlaces);
				console.log("minParts", minParts);
				let minDecimal = minParts[1];
				
				let maxParts = splitNumber(props.range[1], decimalPlaces);
				console.log("maxParts", maxParts);
				let maxDecimal = maxParts[1];
				
				let defParts = splitNumber(myDefVal.value, decimalPlaces);
				console.log("defParts", defParts);
				let defInteger = defParts[0];
				let defDecimal = defParts[1];
				let defIntegerIndex = 0;
				let defDecimalIndex = 0;
				//pickerIndexes.value[0]
				//pickerIndexes.value[2]
				// 生成整数部分
				for (let i = Math.floor(start); i <= end; i++) {
					integerOptions.value.push(String(i));
				
					if (String(i) == defInteger) {
						defIntegerIndex = (integerOptions.value.length - 1);
						console.log("defIntegerIndex000", defIntegerIndex);
					}
				}
				
				// 根据dataType决定是否需要生成小数部分
				if (props.dataType !== 'integer') {
					if (props.dataType === 'float1') {
						for (let i = 0; i < 10; i++) {
							decimalFullOptions.value.push(String(i));
							if (String(i) == defDecimal) {
								defDecimalIndex = i;
								console.log("defDecimalIndex111", defDecimalIndex);
							}
							if (minDecimal <= String(i)) {
								decimalMinOptions.value.push(String(i));
							}
				
							if (maxDecimal >= String(i)) {
								decimalMaxOptions.value.push(String(i));
							}
						}
					} else {
						//两位小数
						for (let i = 0; i < 100; i++) {
							if (i < 10) {
								decimalFullOptions.value.push("0" + i);
								if (("0" + i) == defDecimal) {
									defDecimalIndex = i;
									console.log("defDecimalIndex222", defDecimalIndex);
								}
							} else {
								decimalFullOptions.value.push(String(i));
								if (String(i) == defDecimal) {
									defDecimalIndex = i;
									console.log("defDecimalIndex222", defDecimalIndex);
								}
							}
				
							if (minDecimal <= String(i)) {
								decimalMinOptions.value.push(String(i));
							}
				
							if (maxDecimal >= String(i)) {
								decimalMaxOptions.value.push(String(i));
							}
						}
					}
				}
				
				decimalOptions.value = decimalFullOptions.value;
				console.log("decimalMinOptions", decimalMinOptions);
				console.log("decimalMaxOptions", decimalMaxOptions);
				
				if (props.dataType == 'integer') {
					pickerIndexes.value = [defIntegerIndex];
				} else {
					pickerIndexes.value = [defIntegerIndex, 0, defDecimalIndex];
				}
				
				console.log(pickerIndexes.value);
			}, 300);
			
		},
		{ deep: true, immediate: true }
	);

	// 验证dataType
	watch(
		() => props.dataType,
		(value) => {
			const validDataTypes = ['integer', 'float1', 'float2'];
			if (!validDataTypes.includes(value)) {
				nextTick(() => {
					uni.showModal({
						title: '数据类型错误',
						content: '请传入正确的数据类型枚举',
						showCancel: false,
						confirmText: '我知道了',
						success: () => { },
						fail: () => { },
						complete: () => { }
					});
				});
			}
		},
		{ immediate: true }
	);

	watch(
		() => props.defValue,
		(value) => {

			myDefVal.value = value;

			if (value < props.range[0] || value > props.range[1]) {
				nextTick(() => {
					myDefVal.value = props.range[0];
				});
			}
		},
		{ immediate: true }
	);

	// 验证range
	watch(
		() => props.range,
		([newStart, newEnd]) => {
			if (newStart >= newEnd) {
				nextTick(() => {
					uni.showModal({
						title: '数值区间错误',
						content: '区间起始值必须小于结束值',
						showCancel: false,
						confirmText: '我知道了',
						success: () => { },
						fail: () => { },
						complete: () => { }
					});
				});
			}
		},
		{ deep: true, immediate: true }
	);
</script>
<style scoped>
	.mask {
		width: 100%;
		height: 100vh;
		position: fixed;
		top: 0;
		left: 0;
		background: #000;
		z-index: 900;
		opacity: 0.3;
	}

	.other_popup {
		width: 100%;
		background-color: #fff;
		border-radius: 10upx 10upx 0 0;
		position: fixed;
		left: 0;
		bottom: -1000upx;
		z-index: 999;
		transition: all 0.3s;
	}

	.picker-view {
		width: 750rpx;
		height: 600rpx;
		margin-top: 20rpx;
	}

	.picker-view-column {
		align-items: center;

	}

	.item {
		line-height: 100rpx;
		text-align: center;
	}
</style>