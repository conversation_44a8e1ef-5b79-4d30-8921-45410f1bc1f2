import request from "@/common/request";

/**
 * 实时分页查询任务
 * @param data 请求体参数
 */
export const queryUserDevice = (data: any) => {
  return request({
    method: "POST",
    url: "/device/device/pageList",
    body: data,
    hideLoading: true,
  });
};

/**
 * 启动测量
 * @param data 请求体参数
 */
export const startMeasure = (data: any) => {
  return request({
    method: "POST",
    url: "/abpm/need/device/startPointMeasure",
    body: data,
    hideLoading: false,
  });
};

export const bindDevice = (data: any) => {
  return request({
    method: "POST",
    url: "/monitor/need/device/bindDevice",
    body: data,
    headers: {
      "content-type": "application/x-www-form-urlencoded",
    },
  });
};
export const unbindDevice = (data: any) => {
  return request({
    method: "POST",
    url: "/monitor/need/device/unbindDevice",
    body: data,
    headers: {
      "content-type": "application/x-www-form-urlencoded",
    },
  });
};
