<template>
    <BarHeight></BarHeight>
    <view class="content" :style="{'height': `calc(100% - 64rpx - ${statusBarHeight}px)`}">
        <view class="text-[#1198F3] p-[20rpx] text-[32rpx] pb-[0px]">
            <uni-icons type="smallcircle-filled" size="18" color="#1198F3"></uni-icons>
            <text class="pl-[18rpx]">动态血压血氧监护系统B001</text>
        </view>
        <view>
            <text class="text-[#D43030] text-[24rpx] pl-[72rpx]">注意：请使用2.4G网络进行Wi-Fi配网</text>
        </view>

        <view class="input-item mt-[40rpx] h-[160rpx]">
            <view class="label">配网Wi-Fi名称</view>
            <view class="input" @click="onClickWifiName">
                <text class="flex-1 overflow-hidden">{{ formData.ssid }}</text>
                <uni-icons custom-prefix="iconfont" :type="iconInput" size="18" color="#888"></uni-icons>
            </view>
            <view v-if="is5G" class="pt-[12rpx] text-[24rpx] text-[#D43030]">这可能是一个 5G Wi-Fi, 请选择 2.4G Wi-Fi</view>
        </view>

        <template v-if="connectStatus.active === 0">

            <view class="input-item mt-[50rpx] h-[180rpx]">
                <view class="label">输入密码</view>
                <uni-easyinput type="password" placeholder="请输入Wi-Fi密码" v-model="formData.password" class="easyinput-custom" :clearable="false" :styles="{borderColor: '#ffffff'}"></uni-easyinput>
                <view class="pt-[12rpx] text-[24rpx] text-[#999]">Wi-Fi密码输入错误是最常见的失败原因之一</view>
                <view class="pt-[6rpx] text-[24rpx] text-[#999]">Wi-Fi密码必须至少包含8个字符</view>
            </view>

            <view class="btns">
                <view class="btn">返回</view>
                <view class="btn" style="background: #1198F3; color: white;" @click="onClickConnect">加入</view>
            </view>
        </template>
        <template v-else-if="connectStatus.active !== 0">
            <view class="container-content">
				<view class="animation-box" :class="{ 'animation-stopped': connectStatus.active === 2 }">
					<view class="wave-point"><text>Wi-Fi</text></view>
				</view>
				<view class="step-list">
					<view class="item"><uni-icons custom-prefix="iconfont" :type="iconDeviceConnect" size="18" color="#888"></uni-icons>手机与设备连接</view>
					<view class="item"><uni-icons custom-prefix="iconfont" :type="iconSendMsg" size="18" color="#888"></uni-icons>向设备发送信息</view>
					<view class="item"><uni-icons custom-prefix="iconfont" :type="iconTestNetwork" size="18" color="#888"></uni-icons>设备连接云端，初始化</view>
				</view>
                <view class="text-center text-[32rpx] color-[#D43030]">请检查Wi-Fi网络是否以2.4G频率正确开启</view>
			</view>
        </template>
        <template v-if="connectStatus.active === 2">
            <view v-if="connectStatus.testNetwork === 2" class="btns" style="padding-top: 60rpx;">
                <view class="btn" @click="goBack">返回</view>
                <view class="btn" style="background: #1198F3; color: white;" @click="onClickConnect">重试</view>
            </view>
            <view v-else-if="connectStatus.testNetwork === 1" class="btns" style="padding-top: 60rpx; justify-content: center;">
                <view class="btn" style="background: #1198F3; color: white;" @click="goBack">完成</view>
            </view>
        </template>


        <uni-popup ref="popupWifi" background-color="#fff" borderRadius="40rpx 40rpx 0 0">
			<view class="popup-content">
				<view class="popup-title">
					<text>WiFi列表</text>
					<view class="iconfont icon-shuaxin" @click="getWifiList"></view>
				</view>
				<view class="popup-select">
					<picker-view indicator-style="height: 50px;" style="width: 100%; height: 200px;" :value="index" :immediate-change="true" @change="bindChange">
						<picker-view-column>
							<view v-for="item in wifiList" :key="item.BSSID" style="line-height: 50px; text-align: center;">{{ item.SSID }}</view>
						</picker-view-column>
					</picker-view>

                    <view v-if="loadWifi" class="popup-loading"><u-loading-icon></u-loading-icon></view>
				</view>
				<view class="popup-footer">
					<view class="btn" @click="onClickPopupClose">取消</view>
					<view class="btn" style="background: #1198F3; color: white;" @click="onClickPopupSure">确定</view>
				</view>
			</view>
		</uni-popup>


    </view>
</template>
<script setup lang="ts">
    import { onMounted, onUnmounted, ref, reactive, computed } from 'vue';
    import BarHeight from '@/components/BarHeight.vue';
    import useBlueToothStore from '@/pages/bluetooth/BlueToothData';
    import { testWifiPacket } from '@/utils/wifiPacketTest';

    const statusBarHeight = uni.getSystemInfoSync().statusBarHeight || 0;
    const formData = ref({
        ssid: 'GXYS',
        password: ''
    });

    // 获取蓝牙工具
    const blueToothStore = useBlueToothStore();
    const { wifi_sendConfig } = blueToothStore;


    const connectStatus = reactive({
        active: 0, // 进入设置网络, 0：输入密码，1，进行中，2，已结束
        deviceConnect: 1, // 设备连接，0：进行中，1：连接成功，2：连接失败
        sendMsg: 0, // 发送消息，0：进行中，1：发送成功，2：发送失败
        testNetwork: 0, // 测试网络，0：进行中，1：测试成功，2：测试失败
    })

    const onClickConnect = async () => {

        // 测试
        // testPacketFormat()
        // return

        if (!formData.value.ssid || !formData.value.password) {
            uni.showToast({
                title: 'Wi-Fi名称或密码必填',
                icon: 'none'
            })
            return
        }

        if (formData.value.password.length < 8) {
            uni.showToast({
                title: 'Wi-Fi密码必须至少包含8个字符',
                icon: 'none'
            })
            return
        }

        // 开始连接流程
        connectStatus.active = 1;
        connectStatus.deviceConnect = 1; // 假设设备已连接
        connectStatus.sendMsg = 0; // 开始发送消息

        try {
            // 发送WiFi配置
            console.log('开始发送WiFi配置:', {
                ssid: formData.value.ssid,
                password: formData.value.password
            });

            const success = await wifi_sendConfig(formData.value.ssid, formData.value.password);

            if (success) {
                console.log('WiFi配置发送成功');
                connectStatus.sendMsg = 1; // 发送成功
                connectStatus.testNetwork = 0; // 开始测试网络

                // 模拟网络测试过程
                setTimeout(() => {
                    // 这里应该根据实际的网络测试结果来设置
                    // 暂时模拟成功
                    connectStatus.testNetwork = 1; // 测试成功
                    connectStatus.active = 2; // 流程结束
                }, 3000);

            } else {
                console.error('WiFi配置发送失败');
                connectStatus.sendMsg = 2; // 发送失败
                connectStatus.testNetwork = 2; // 测试失败
                connectStatus.active = 2; // 流程结束

                uni.showToast({
                    title: 'WiFi配置发送失败',
                    icon: 'error'
                });
            }
        } catch (error) {
            console.error('WiFi配置发送异常:', error);
            connectStatus.sendMsg = 2; // 发送失败
            connectStatus.testNetwork = 2; // 测试失败
            connectStatus.active = 2; // 流程结束

            uni.showToast({
                title: 'WiFi配置发送异常',
                icon: 'error'
            });
        }
    }

    // 测试数据包格式
    const testPacketFormat = () => {
        console.log('=== 测试WiFi数据包格式 ===');
        testWifiPacket(formData.value.ssid, formData.value.password);
    }

    
    const goBack = () => {
		uni.navigateBack();
	}

    const is5G = computed(() => {
        if (formData.value.ssid?.includes('5G') || formData.value.ssid?.includes('5g')) {
            return true
        }
        return false
    })

    const iconInput = computed(() => {

        // 输入密码阶段
        if (connectStatus.active === 0) {
            return 'icon-xiala'
        }

        // 没有连接设备成功
        if(connectStatus.deviceConnect !== 1) {
            return 'icon-jiazai icon-rotating'
        }

        // 发送状态
        if (connectStatus.sendMsg === 0) {
            // 旋转加载
            return 'icon-jiazai icon-rotating'
        } else if (connectStatus.sendMsg === 1) {
            // 成功打勾图
            return 'icon-dagou blue'
        } else if (connectStatus.sendMsg === 2) {
            // 失败叉叉图
            return 'icon-close red'
        }

        return 'icon-xiala'
    })

    const iconDeviceConnect = computed(() => {
        if (connectStatus.deviceConnect === 0) {
            // 旋转加载
            return 'icon-jiazai icon-rotating'
        } else if (connectStatus.deviceConnect === 1) {
            // 成功打勾图
            return 'icon-dagou blue'
        } else if (connectStatus.deviceConnect === 2) {
            // 失败叉叉图
            return 'icon-close red'
        }
    })

    const iconSendMsg = computed(() => {
        if (connectStatus.sendMsg === 0) {
            // 旋转加载
            return 'icon-jiazai icon-rotating'
        } else if (connectStatus.sendMsg === 1) {
            // 成功打勾图
            return 'icon-dagou blue'
        } else if (connectStatus.sendMsg === 2) {
            // 失败叉叉图
            return 'icon-close red'
        }
    })

    const iconTestNetwork = computed(() => {
        if (connectStatus.testNetwork === 0) {
            // 旋转加载
            return 'icon-jiazai icon-rotating'
        } else if (connectStatus.testNetwork === 1) {
            // 成功打勾图
            return 'icon-dagou blue'
        } else if (connectStatus.testNetwork === 2) {
            // 失败叉叉图
            return 'icon-close red'
        }
    })



    // WIFI 方法

    const popupWifi = ref<any>({})
    const wifiList = ref<any>([])
    const index = ref([0])
    const loadWifi = ref(true)
    


    /** 启动wifi */
    const startWifi = () => {
        return new Promise((resolve, reject) => {
            uni.startWifi({
                success: (res) => {
                    console.log('启动wifi 成功', res)
                    resolve(true)
                },
                fail: (err) => {
                    console.error('启动wifi 失败', err)
                    uni.showModal({ content: err.errMsg, showCancel: false })
                    reject(err)
                },
            })
        })
    }

    const onGetWifiList = () => {
        uni.onGetWifiList(res => {
            console.log('监听到变化')
            console.log(res.wifiList)
            wifiList.value = res.wifiList.filter(item => item.SSID);
            // if (loadWifi.value) {
            //     loadWifi.value = false;
            // }
            // console.log(loadWifi.value)
            // if (loadWifi.value) {
            // 	loadWifi.value = false;
            // 	popupWifi.value?.open('bottom');
            // }

        })
    }

    const getWifiList = async () => {

        const hasStart = await startWifi()
        if (hasStart !== true) return

        // 先打开弹窗
        loadWifi.value = true;
        popupWifi.value?.open('bottom');

        onGetWifiList()

        uni.getWifiList({
            success: () => {
                loadWifi.value = false;
                console.log('获取列表成功！')
            },
            fail: (res) => {
                if (res.errMsg === 'getWifiList:fail:auth denied') {
                    uni.showToast({
                        title: '拒绝获取位置，无法读取WiFi列表！',
                        icon: 'none',
                        duration: 3000
                    })
                }else if (res.errMsg === 'getWifiList:fail auth deny') {
                    uni.hideToast();
                    // 授权失败！
                    uni.showModal({
                        title: '提示',
                        content: '获取WiFi列表，需要把APP权限中的位置信息-改为允许！',
                        success (res) {
                            if (res.confirm) {
                            }
                        }
                    })
                }else {
                    uni.hideToast();
                    uni.showToast({
                        title: '获取失败！',
                        icon: 'none',
                        duration: 3000
                    })
                }
            }
        })
    }
    const bindChange = (e: any) => {
        index.value[0] = e.detail.value;
    }
    // 关闭选择 wifi
    const onClickPopupClose = () => {


        // #ifdef APP-PLUS
        uni.offGetWifiList()

        uni.stopWifi({
            success: () => {
                console.log('关闭wifi 成功')
            },
            fail: (err) => {
                console.error('关闭wifi 失败', err)
            },
        })
        // #endif
        popupWifi.value?.close();
    }
    // 点击确认改变 wifi
    const onClickPopupSure = () => {
        if (loadWifi.value) {
            uni.showToast({
                title: '等待加载',
                icon: 'none',
                duration: 2000
            })
            return
        }

        formData.value.ssid  = wifiList.value[index.value[0]]?.SSID;

        console.log(formData.value)

        popupWifi.value?.close();
    }

    const onClickWifiName = () => {

        // popupWifi.value?.open('bottom');
        // return

        if (connectStatus.active !== 0){
            return
        }

        getWifiList();
        // if (wifiList.value.length) {
        //     popupWifi.value?.open('bottom');
        // }else {
        //     console.log(uni.getSystemInfoSync())
        //     // if (uni.getSystemInfoSync().platform === 'ios') {
        //     //     // 苹果系统，需要在弹出教程
        //     //     this.$refs.popupCourse.open('bottom');
        //     //     return;
        //     // }
        //     getWifiList();
        // }
    }

    
    // Wi-Fi 结束

    onMounted(() => {
        // #ifdef APP-PLUS
        // #endif
    })

</script>
<style scoped lang="scss">
    page {
        width: 100%;
        height: 100%;
        background: #F7F9FC;
    }
    .content {
        position: relative;
        display: flex;
        flex-direction: column;
        background: #FFFFFF;
        box-shadow: 0px 4rpx 20rpx rgba(42, 130, 228, 0.15);
        border-radius: 16rpx;
        width: calc(100% - 64rpx);
        height: calc(100% - 64rpx);
        margin: 32rpx;
    }
    .input-item {
        padding: 20rpx 90rpx;
        .label {
            color: #000;
            font-size: 28rpx;
            margin-bottom: 14rpx;
        }
        .input {
            height: 70rpx;
            line-height: 70rpx;
            border-radius: 18rpx;
            background: rgba(250, 250, 250, 1);
            box-shadow: 0px 4rpx 8rpx  rgba(56, 56, 56, 0.25);
            display: flex;
            padding: 0 18rpx;
        }
        .easyinput-custom {
            height: 70rpx;
            line-height: 70rpx;
            border-radius: 18rpx;
            background: rgba(250, 250, 250, 1);
            box-shadow: 0px 4rpx 8rpx  rgba(56, 56, 56, 0.25);
        }
    }

    .btns {
        display: flex;
        justify-content: space-between;
        padding: 100rpx;
    }
    .btn {
        width: 160rpx;
        height: 70rpx;
        line-height: 70rpx;
        text-align: center;
        border-radius: 16rpx;
        background: rgba(235, 243, 253, 1);
        box-shadow: 0px 4rpx 8rpx  rgba(17, 152, 243, 0.25);
    }


    .animation-box{
        width: 500rpx;
        height: 500rpx;
        margin: auto;
        display: flex;
        align-items: center;
        justify-content: center;

        // 当连接状态为2时停止动画
        &.animation-stopped {
            .wave-point {
                text:before,
                &:before,
                &:after {
                    animation-play-state: paused;
                }
            }
        }
    }
    .step-list{
        margin: 20rpx auto 0;
        width: 400rpx;
        .item {
            font-size: 32rpx;
            line-height: 32rpx;
            margin-bottom: 30rpx;
            color: rgba(77, 77, 77, 1);
            .iconfont{
                margin-right: 20rpx;
                &.blue{
                    color: #147BD1;
                }
            }
        }
    }
    /* 波动效果 */
    .wave-point {
        display: inline-block;
        position: relative;
        width: 200rpx;
        height: 200rpx;
        text {
            position: absolute;
            width: 200rpx;
            height: 200rpx;
            text-align: center;
            line-height: 200rpx;
            color: #ffffff;
            background: #1198F3;
            border-radius: 50%;
            z-index: 1;
            &:before{
                position: absolute;
                left: 17%;
                top: 17%;
                content: "";
                width: 66%;
                height: 66%;
                border-radius: 50%;
                opacity: 0;
                background: rgba(17, 152, 243, 0.5);
                animation: circle-opacity 1.6s infinite;
                z-index: -1;
            }
        }
    &:before{
        position: absolute;
        left: 9%;
        top: 9%;
        content: "";
        width: 82%;
        height: 82%;
        border-radius: 50%;
        opacity: 0;
        background: #c5d9ff;
        animation: circle-opacity 1.6s infinite;
    }
    &:after {
        position: absolute;
        left: 1%;
        top: 1%;
        content: "";
        width: 98%;
        height: 98%;
        border-radius: 50%;
        opacity: 0;
        background: #cfe0ff;
        animation: circle-opacity 1.6s infinite;
    }
    }
    @keyframes circle-opacity{
        from {
            opacity: 1;
            transform: scale(1);
        }
        to {
            opacity: 0;
            transform: scale(2.4);
        }
    }
    .blue {
        color: #1198F3 !important;
    }
    .red {
        color: #FF4D4F !important;
        font-weight: bold;
    }


    .popup-content{
        .popup-title {
            font-size: 28rpx;
            text-align: center;
            margin-top: 40rpx;
            .iconfont{
                color: #147BD1;
                position: absolute;
                right: 40rpx;
                font-size: 32rpx;
                font-weight: bold;
            }
        }
        .popup-select{
            padding: 20rpx 0;
            margin-top: 40rpx;
            position: relative;
            .popup-loading {
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                display: flex;
                justify-content: center;
                background: #dedede21;
                z-index: 99;
            }
        }
        .popup-footer{
            display: flex;
            justify-content: space-evenly;
            padding: 40rpx 0;
            border-top: 2rpx solid #eee;
        }
    }
</style>