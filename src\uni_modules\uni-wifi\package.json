{"id": "uni-wifi", "displayName": "uni-wifi", "version": "1.0.6", "description": "wifi管理", "keywords": ["wifi"], "repository": "", "engines": {"HBuilderX": "^3.96"}, "dcloudext": {"type": "uts", "sale": {"regular": {"price": "0.00"}, "sourcecode": {"price": "0.00"}}, "contact": {"qq": ""}, "declaration": {"ads": "无", "data": "插件不采集任何数据", "permissions": "Android平台：\n<uses-permission android:name=\"android.permission.ACCESS_WIFI_STATE\"/>\n<uses-permission android:name=\"android.permission.CHANGE_WIFI_STATE\"/>\n<uses-permission android:name=\"android.permission.ACCESS_FINE_LOCATION\"/>\n<uses-permission android:name=\"android.permission.ACCESS_COARSE_LOCATION\"/>\niOS平台：\niOS13及以上平台获取Wifi信息需要定位权限"}, "npmurl": ""}, "uni_modules": {"uni-ext-api": {"uni": {"startWifi": {"web": false}, "stopWifi": {"web": false}, "connectWifi": {"web": false}, "getConnectedWifi": {"web": false}, "getWifiList": {"web": false}, "onGetWifiList": {"web": false}, "offGetWifiList": {"web": false}, "onWifiConnected": {"web": false}, "offWifiConnected": {"web": false}, "onWifiConnectedWithPartialInfo": {"web": false}, "offWifiConnectedWithPartialInfo": {"web": false}}}, "dependencies": [], "encrypt": [], "platforms": {"cloud": {"tcb": "y", "aliyun": "y"}, "client": {"Vue": {"vue2": "y", "vue3": "y"}, "App": {"app-android": {"minVersion": "19"}, "app-ios": {"minVersion": "9"}}, "H5-mobile": {"Safari": "n", "Android Browser": "n", "微信浏览器(Android)": "n", "QQ浏览器(Android)": "n"}, "H5-pc": {"Chrome": "n", "IE": "n", "Edge": "n", "Firefox": "n", "Safari": "n"}, "小程序": {"微信": "n", "阿里": "n", "百度": "n", "字节跳动": "n", "QQ": "n", "钉钉": "n", "快手": "n", "飞书": "n", "京东": "n"}, "快应用": {"华为": "n", "联盟": "n"}}}}}