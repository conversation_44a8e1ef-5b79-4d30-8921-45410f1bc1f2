// stores/nfcInfo.ts
import { defineStore } from 'pinia'

export const useNfcInfoStore = defineStore('nfcInfo', {
  state: () => ({
    nfc_avail: false, // NFC 是否可用
    nfc_id: '',       // 读取到的设备 ID
  }),

  getters: {
    isNfcAvailable: (state) => state.nfc_avail,
    getNfcId: (state) => state.nfc_id,
  },

  actions: {
    // 设置 NFC 可用状态
    setNfcAvail(status: boolean) {
      this.nfc_avail = status
    },

    // 设置读取到的 NFC 设备 ID
    setNfcId(id: string) {
      this.nfc_id = id
    },

    // 假设有一个方法来模拟读取 NFC 设备 ID
    async readNfc() {
      if (this.nfc_avail) {
        // 模拟读取 NFC 设备 ID (这里可以替换成实际的 NFC 读取逻辑)
        const simulatedId = 'NFC1234567890'
        this.setNfcId(simulatedId)
        console.log('NFC 设备 ID 已读取:', simulatedId)
      } else {
        console.error('NFC 不可用')
      }
    },

    // 假设有一个方法来检查 NFC 可用性
    async checkNfcAvailability() {
      // 这里可以替换为实际的 NFC 可用性检查逻辑
      const isAvailable = true  // 模拟 NFC 可用
      this.setNfcAvail(isAvailable)
      console.log('NFC 可用状态:', isAvailable)
    },
  }
})
