<template>
    <view class="bg-[#e3ecf2] h-full">
        <z-paging 
        ref="refPaging" 
        v-model="dataList" 
        @query="queryList" 
        fixed
        :scroll-with-animation="true"
        :empty-view-z-index="0" 
        default-page-size="999" 
        :loading-more-enabled="false"
        refresher-background="#e3ecf2">
        <template #top>
            <view class="bg-white py-[30rpx] px-[10rpx] mt-[1px] flex flex-row justify-center items-center" style="border-bottom: 1px solid #eee;">
                <text class="text-[36rpx] font-bold pr-[20rpx]">{{ patient.patientName }}</text>
                <text class="pr-[16rpx]">{{ patient.sexText }}</text>
                <text class="pr-[32rpx]">{{ patient.age }}</text>
                <text class="pr-[16rpx]">预约时间：{{ moment(patient.appointmentTime).format('YYYY-MM-DD') || '-' }}</text>
            </view>
        </template>
        <view v-if="dataList.length" class="p-[30rpx] bg-[#E3ECF2]">
            <view v-for="item in dataList" class="p-[30rpx] pb-[40rpx] bg-white mb-[30rpx] text-[32rpx] rounded-[16rpx]">
                <dataItem :item="item"></dataItem>
            </view>
            <view class="h-[80rpx]"></view>
        </view>
        <template #empty>
            <up-empty mode="list" text="没有测量数据" icon="/static/img/empty1.png" height="100" width="174"></up-empty>
        </template>
        </z-paging>
    </view>
</template>
<script setup lang="ts">
import { ref, reactive, onMounted, onBeforeUnmount, computed, nextTick } from 'vue';
import { onLoad, onShow, onUnload } from "@dcloudio/uni-app";
import { queryPatientMeasureRecord } from '@/common/api/task';
import moment from 'moment';
import dataItem from './components/dataItem.vue'

const dataList: any = ref([])
const patient: any = ref({})
const bpMeasureType: any = ref('')
const refPaging: any = ref({});

const queryList = (pageNo: any, pageSize: any) => {
    const params = {
        bpMeasureType: bpMeasureType.value, // 家庭测量
        patientId: patient.value.patientId
    }
    queryPatientMeasureRecord(params, false).then(res => {
        const data = res.data || []
        refPaging.value.complete(data);
    }).catch(() => {
        refPaging.value.complete([]);
    })
}

onLoad((option: any) => {
    const patientData = JSON.parse(decodeURIComponent(option.patient));
    
    patient.value = patientData
    bpMeasureType.value = option.bpMeasureType

    const title = ['测量记录', '双臂诊室测量', '体位诊室测量', '院外测量', '二次诊室测量']

    if (title[bpMeasureType.value]) {
        uni.setNavigationBarTitle({
            title: title[bpMeasureType.value]
        })
    }
})
</script>
<style scoped lang="scss">
page {
    background: #e3ecf2;
    height: 100%;
}
</style>