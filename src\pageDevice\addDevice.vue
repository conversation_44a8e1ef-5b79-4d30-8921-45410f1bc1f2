<template>
    <view class="container h-full flex flex-col">
        <view class="item-01 flex items-center justify-center flex-col">
            <view @click="onClickScanDevice" class="saoma flex items-center justify-center">
                <!-- <text class="iconfont icon-saoma"></text> -->
                <uni-icons type="scan" size="74" color="#0052A8"></uni-icons>
            </view>
            <text class="text-saoma" @click="onClickScanDevice">点击扫一扫</text>
            <text>设备、外包装上的二维码</text>
        </view>
        <view class="item-02">
            <uni-easyinput class="input uni-mt-5" trim="all" v-model="form.deviceNo" placeholder="或输入设备编号"></uni-easyinput>
            <view class="action" @click="onClickAddDevice">确定</view>
            <text class="tips" @click="onClickBack">返回</text>
        </view>
    </view>
</template>
<script>
import { bindDevice } from '@/common/api/device'
import useScanCode from './useScanCode.js'
import { useUserInfo } from '@/stores/userInfo'

export default {
    data() {
        return {
            form: {}
        }
    },
    methods: {
        onClickBack() {
            uni.navigateBack({})
        },
        onClickScanDevice() {
            // 扫一扫
            useScanCode(deviceNo => {
                uni.navigateBack({})
            })
        },
        onClickAddDevice() {
            // 没有填
			if (!this.form.deviceNo) {
				uni.showToast({
					title: '设备编码必须',
					icon: 'error'
				})
				return;
			}
            const storeUserInfo = useUserInfo();
            const params = Object.assign({userId: storeUserInfo.userId}, this.form)
			bindDevice(params).then(res => {
				if (res.success) {
                    uni.navigateBack({
                        success: () => {
                            uni.showToast({
                                title: '绑定成功！',
                                icon: 'success'
                            })
                        }
                    });
				}else {
                    uni.showToast({
                        title: res.msg,
                        icon: 'none'
                    })
                }
			})
        }
    }
}
</script>
<style lang="scss" scoped>
page {
    background: #F7F9FC;
	height: 100%;
}
.container {
    .item-01 {
        height: 54%;
        background: #237fc2;
        color: white;
        .saoma {
            width: 260rpx;
            height: 260rpx;
            border-radius: 50%;
            background: #00DCA7;
        }
        .text-saoma {
            padding: 50rpx 0 12rpx;
        }
    }
    .item-02 {
        padding: 76rpx 76rpx 0;
        .input {
            ::v-deep .uni-easyinput__content {
                height: 80rpx;
                border-radius: 40rpx;
                border: none;
                padding-left: 20rpx;
                background-color: #DCEDFF !important;
                .uni-easyinput__content-input {
                    border-radius: 40rpx;
                    background: #DCEDFF;
                    height: 100%;
                    .uni-input-placeholder {
                        color: #A3BCD8;
                        font-size: 28rpx;
                    }
                }
            }
        }
        .action {
            width: 100%;
            height: 80rpx;
            line-height: 80rpx;
            border-radius: 16rpx;
            text-align: center;
            color: white;
            margin-top: 44rpx;
            background: #237fc2;
        }
        .tips {
            position: absolute;
            width: 120rpx;
            text-align: center;
            color: #A3BCD8;
            bottom: 100rpx;
            left: calc(50% - 60rpx);
            text-decoration-line: underline;
        }
    }
}
// .bg {
//     width: 240rpx;
//     height: 310rpx;
// }
// .action-box {

//     width: 100%;
//     height: 500rpx;
//     background: white;
//     padding-top: 20rpx;
//     padding-bottom: 200rpx;
//     .action {
//         width: 260rpx;
//         height: 68rpx;
//         line-height: 68rpx;
//         text-align: center;
//         border-radius: $xx-border-radius;
//         border: 1px solid $xx-color-primary;
//         color: $xx-color-primary;
//         margin: 20rpx 0;
//     }
// }
</style>