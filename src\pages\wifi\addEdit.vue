<template>
    <view class="bg-[#e3ecf2] p-[30rpx]">
        <view class="flex flex-col">
            <uni-forms ref="baseForm" :modelValue="paramas">
                <uni-forms-item label="账号:" name="name" label-width="48">
                    <uni-easyinput type="text" v-model="paramas.wifiName" placeholder="请输入账号" :clearable="false"/>
                </uni-forms-item>
                <uni-forms-item label="密码:" name="name" label-width="48">
                    <uni-easyinput type="password" v-model="paramas.wifiPassword" placeholder="请输入密码" :clearable="false"/>
                </uni-forms-item>
            </uni-forms>
            <view @click="doSave()" class="flex flex-1 justify-center text-white bg-[#0052A8] rounded-[16rpx] mt-[50rpx] px-[32rpx] py-[24rpx]">保存</view>
            <view @click="docancel()" class="flex flex-1 justify-center text-white bg-[#BEBEBE] rounded-[16rpx] mt-[50rpx] px-[32rpx] py-[24rpx]">取消</view>
        </view>
        
    </view>
</template>
<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { onLoad } from '@dcloudio/uni-app';
import {
    createPatientWifi,
    editPatientWifi
} from '@/common/api/patientWifi'

import type {
    PatientsInfo, Wifi
} from '@/models/patientWifi'
// 定义 form 的类型
interface Form {
    wifiName: string;
    wifiPassword: string;
}

const paramas = ref<Wifi>()
// 使用 ref 定义 form 响应式对象
const form = ref<Form>({
    wifiName: '',
    wifiPassword: ''
});
// onLoad(() => {
//   const options = uni.getLaunchOptionsSync();
//   const patientName = options.query.patientName; // 获取传递过来的 patientName
//   console.log('Received patientName~~~:', patientName);
// });
onLoad((option: any) => {
    //{"type":"1"}
    console.log('wifi编辑页面【onLoad】：页面加载完成', JSON.stringify(option));
    paramas.value = JSON.parse(decodeURIComponent(option.paramas));

    console.log('paramas.value：', paramas.value);
     uni.setNavigationBarTitle({ title: paramas.value?.patientName || '' })


})

const doSave = () => {

    console.log('doSave:', JSON.stringify(paramas.value));
    if (!paramas.value || !paramas.value.wifiName) {
        uni.showToast({
            title: '名称不能为空',
            icon: 'none'
        })

        return
    }

    if (!paramas.value.wifiPassword) {
        uni.showToast({
            title: '密码不能为空',
            icon: 'none'
        })

        return
    }

    let body = {
        patientId: paramas.value?.patientId,
        wifiId: paramas.value?.wifiId,
        wifiName: paramas.value?.wifiName,
        wifiPassword: paramas.value?.wifiPassword
    }

    const apiObj = paramas.value.wifiId ? editPatientWifi : createPatientWifi


    apiObj(body).then((res: any) => {
        if (!res.success) {
            uni.showToast({
                title: res.msg,
                icon: 'none'
            })
            return
        }
        //成功后返回上个页面
        uni.navigateBack()
    }).catch((error) => {
        console.log(error);
        uni.showToast({
            title: error,
            icon: 'none'
        })
    })



}
const docancel=()=>{
    uni.navigateBack()
}
</script>
<style scoped lang="scss">
:deep(.is-input-border) {
    border: none;
    border-bottom: 1px solid #dcdfe6;
}
:deep(.uni-easyinput__content-input) {
    font-size: 38rpx;
    height: 92rpx;
}
:deep(.uni-forms-item__label){
    font-size: 38rpx;
    height: 92rpx;
    line-height: 92rpx;
}
</style>