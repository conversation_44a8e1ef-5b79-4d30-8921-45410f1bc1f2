<template>
    <view class="mb-[20rpx]">
        <view v-if="props.steps === 10" class="top-filter">
            <view v-for="(item, key) in options.arm" :key="key"
                :class="{ 'filter-box': 1, 'highlight': item.itemValue == formData.arm }" @click="changeArm(item)">
                <view class="up">
                    <text class="t">{{ item.itemName }}</text>
                </view>
            </view>
        </view>
        <view style="background-color: #E3ECF2;position: sticky;z-index: 1;top: 0px;">
            <view class="text-[34rpx] py-[30rpx] flex justify-between items-center">
                <text style="text-shadow: 3px 3px 3px #b9b9b9;">诊室测量-双臂压差分析</text>
                <view v-if="props.steps === 10" @click="onClickSure" :class="{'bg-[#BEBEBE]': armList.length == 0 }" class="w-[220rpx] py-[16rpx] text-center rounded-[40rpx] bg-[#237FC2] color-white">完成分析</view>
                <uni-icons v-if="props.steps > 10" custom-prefix="iconfont" type="icon-dagou"size="28" color="#1ABA62"></uni-icons>
            </view>
        </view>
        <!-- 已完成双臂压差 -->
        <view v-if="props.steps > 10">
            <view class="bg-white p-[20rpx] rounded-[16rpx]">
                <view class=" text-[34rpx] flex justify-between items-center">
                    <view>使用<text class="color-[#1ABA62] text-[44rpx] px-[8rpx]">{{ formData.arm === 1 ? '左臂' : '右臂' }}</text>佩戴设备</view>
                </view>
                <view class="pt-[40rpx] pb-[8rpx] flex justify-between items-center">
                    <view class="color-[#0d0d0d] text-[28rpx]">双臂压差分析结果</view>
                    <view class="flex">
                        <!-- 体位显示左右臂重试 -->
                        <view v-if="props.steps <= 30 && props.device.planJson == null" @click="onReset" class="flex justify-between items-center pr-[20rpx]"><uni-icons custom-prefix="iconfont" type="icon-reset"size="20" color="#1296DB"></uni-icons><text class="pl-[8rpx] text-[28rpx] color-[#999] font-600">重测</text></view>

                        <view @click="showRecommend = true" class="flex justify-between items-center pl-[20rpx]"><uni-icons custom-prefix="iconfont" type="icon-down"size="20" color="#1296DB"></uni-icons><text class="pl-[12rpx] text-[28rpx] color-[#999] font-600">详情</text></view>
                    </view>
                </view>
            </view>
        </view>
        <view v-else>
            <view class="p-[30rpx] bg-white rounded-[24rpx] flex justify-between">
                <text class="text-[28rpx]">臂围</text>
                <view @click="pickerArmGirth = true">{{ formData.armGirth }} CM</view>
            </view>

            <view class="p-[30rpx] bg-white rounded-[24rpx] flex justify-between mt-[30rpx]">
                <text class="text-[28rpx]">袖带</text>
                <view @click="pickerCuff = true">
                    <text class="pr-[80rpx]">{{ (formData.cuffType && options.cuffType[formData.cuffType - 1]?.name) || '-' }}</text>
                    <text>{{ (formData.cuffSize && options.cuffSize[formData.cuffSize - 1]?.name) || '-' }}</text>
                </view>
            </view>
            <view v-if="armList.length" class="pt-[30rpx]">
                <view v-for="(item, index) in armList" @dblclick="onEditItem(item, index)" class="p-[30rpx] pb-[40rpx] bg-white mb-[30rpx] text-[32rpx]">
                    <dataItem :item="item" :arm="true"></dataItem>
                </view>
            </view>
            <view v-else class="p-[50rpx]  bg-white rounded-[24rpx] mt-[30rpx]">
                <up-empty mode="list" text="无双臂压差测量" icon="/static/img/empty1.png" height="83" width="145"></up-empty>
            </view>
        </view>

        <up-picker :defaultIndex="[defIdxs.armGirth]" :show="pickerArmGirth" :columns="armGirthOption" @cancel="pickerArmGirth = false" @confirm="onArmGirth" :immediate-change="true"></up-picker>
        <up-picker :defaultIndex="[defIdxs.cuffType, defIdxs.cuffSize]" :show="pickerCuff" :columns="cuffSOption" @cancel="pickerCuff = false" @confirm="onCuff" keyName="name" immediateChange></up-picker>

        <!-- <root-portal> -->
            <up-popup :show="showRecommend" mode="center" round="2" @close="showRecommend = false">
                <view class="w-[600rpx]">
                    <view class="h-[120rpx] text-center text-[40rpx] bg-[#EFEFEF] mb-[20rpx]" style="line-height: 120rpx;">双臂压差分析结果</view>
                    <view class="mb-[40rpx]">
                        <view class="text-center text-2xl mb-[20rpx]">左臂</view>
                        <view class="flex">
                            <view class="flex flex-col items-center flex-1">
                                <text class="text-[44rpx] pt-[20rpx] pb-[30rpx]">{{ layer.left.sbp }}</text>
                                <text class="text-[28rpx]">平均收缩压</text>
                            </view>
                            <view class="flex flex-col items-center flex-1">
                                <text class="text-[44rpx] pt-[20rpx] pb-[30rpx]">{{ layer.left.dbp }}</text>
                                <text class="text-[28rpx]">平均舒张压</text>
                            </view>
                            <view class="flex flex-col items-center flex-1">
                                <text class="text-[44rpx] pt-[20rpx] pb-[30rpx]">{{ layer.left.pulse }}</text>
                                <text class="text-[28rpx]">平均脉搏</text>
                            </view>
                        </view>
                    </view>
                    <view class="mb-[40rpx]">
                        <view class="text-center text-2xl mb-[20rpx]">右臂</view>
                        <view class="flex">
                            <view class="flex flex-col items-center flex-1">
                                <text class="text-[44rpx] pt-[20rpx] pb-[30rpx]">{{ layer.right.sbp }}</text>
                                <text class="text-[28rpx]">平均收缩压</text>
                            </view>
                            <view class="flex flex-col items-center flex-1">
                                <text class="text-[44rpx] pt-[20rpx] pb-[30rpx]">{{ layer.right.dbp }}</text>
                                <text class="text-[28rpx]">平均舒张压</text>
                            </view>
                            <view class="flex flex-col items-center flex-1">
                                <text class="text-[44rpx] pt-[20rpx] pb-[30rpx]">{{ layer.right.pulse }}</text>
                                <text class="text-[28rpx]">平均脉搏</text>
                            </view>
                        </view>
                    </view>
                    <view class="py-[40rpx] text-center">压差 <text class="text-3xl font-bold px-[20rpx]">{{ layer.diff.sbp }}</text> mmHg</view>
                    <view v-if="props.steps > 10" class="flex h-[100rpx] w-full text-[40rpx] leading-[100rpx] text-center">
                        <view class="flex-1 bg-[#1296DB] color-white" @click="showRecommend = false">{{ formData.arm === 1 ? '左手臂' : '右手臂'}}</view>
                    </view>
                    <view v-else-if="layer.diff.recommend === 1" class="flex h-[100rpx] w-full text-[40rpx] leading-[100rpx] text-center">
                        <view class="flex-1 bg-[#1296DB] color-white" @click="onClickSubmit(1)">推荐左臂</view>
                        <view class="px-[40rpx] text-[32rpx] bg-[#ededed]" @click="onClickSubmit(2)">使用右臂</view>
                    </view>
                    <view v-else class="flex h-[100rpx] w-full text-[40rpx] leading-[100rpx] text-center">
                        <view class="px-[40rpx] text-[32rpx] bg-[#ededed]" @click="onClickSubmit(1)">使用左臂</view>
                        <view class="flex-1 bg-[#1296DB] color-white" @click="onClickSubmit(2)">推荐右臂</view>
                    </view>
                </view>
            </up-popup>



            <!-- 编辑弹窗 -->
            <up-popup :show="showEditDialog" mode="center" round="8" @close="showEditDialog = false">
                <view class="w-[600rpx] bg-white p-[40rpx]">
                    <view class="text-center text-[40rpx] mb-[40rpx]">编辑测量数据</view>
                    
                    <up-form :model="editForm" label-width="140rpx">
                        <up-form-item label="有效性">
                            <up-radio-group v-model="editForm.valid">
                                <up-radio 
                                    v-for="item in options.valid" 
                                    :key="item.itemValue"
                                    :label="item.itemName"
                                    :name="item.itemValue"
                                    :disabled="item.itemValue === '0'"
                                />
                            </up-radio-group>
                        </up-form-item>
                        <up-form-item label="收缩压">
                            <up-input v-model="editForm.sbp" type="number"/>
                        </up-form-item>
                        <up-form-item label="舒张压">
                            <up-input v-model="editForm.dbp" type="number"/>
                        </up-form-item>
                        <up-form-item label="脉搏">
                            <up-input v-model="editForm.pulse" type="number"/>
                        </up-form-item>
                        <up-form-item label="手臂">
                            <up-radio-group v-model="editForm.arm">
                                <up-radio 
                                    v-for="arm in options.arm" 
                                    :key="arm.itemValue"
                                    :label="arm.itemName"
                                    :name="arm.itemValue"
                                />
                            </up-radio-group>
                        </up-form-item>
                    </up-form>

                    <view class="flex justify-around mt-[60rpx]">
                        <up-button @click="showEditDialog = false">取消</up-button>
                        <up-button type="primary" @click="onSaveEdit">保存</up-button>
                    </view>
                </view>
            </up-popup>
        <!-- </root-portal> -->
    </view>
</template>
<script setup lang="ts">
import { ref, reactive, onMounted, onBeforeUnmount, computed, defineExpose } from 'vue';
import moment from 'moment';
import { deepClone } from '@/uni_modules/uview-plus';
import dataItem from './dataItem.vue'

import { queryPatientMeasureRecord, updateMeasureParams, updateMeasureStatus, updateArm, editMeasureRecord } from '@/common/api/task';
import { useUserInfo } from '@/stores/userInfo';
const storeUserInfo = useUserInfo();

const emits = defineEmits(['changeArm'])

const props = defineProps({
    patient: {
        type: Object,
        default: () => {},
    },
    steps: {
        type: Number,
        default: 0
    },
    device: {
        type: Object,
        default: () => {},
    },
})

const options = {
    cuffType: [
        {value: 1, name: '公共袖带' },
        {value: 2, name: '一人次袖带'}
    ],
    cuffSize: [
        {value: 1, name: '小' },
        {value: 2, name: '中'},
        {value: 3, name: '大'}
    ],
    arm: storeUserInfo.dictCodeInfo?.arm || [],
    valid: storeUserInfo.dictCodeInfo?.valid || []
}

// 表单
const formData = reactive({
    arm: 1,
    armGirth: 30,
    cuffSize: 1,
    cuffType: 1,
})


const defIdxs = reactive({
    armGirth: 12,
    cuffType: 1,
    cuffSize: 1,
})

// 左右臂
const armList: any = ref([
    // {sbp: 100, dbp: 80, pulse: 70, measureTime: new Date(), posture: 0},{}
])

const layer = reactive({
    left: { sbp: 0, dbp: 0, pulse: 0, arm: '' },
    right: { sbp: 0, dbp: 0, pulse: 0, arm: '' },
    diff: { sbp: 0, dbp: 0, pulse: 0, arm: '', recommend: 1 }
})

// 获取测量记录
function queryList() {
    const params = {
        bpMeasureType: 10, // 双臂诊室测量
        patientId: props.patient.patientId
    }
    queryPatientMeasureRecord(params).then(res => {
        armList.value = res.data || []

        const allData = res.data.filter((item: any) => item.measureType === 1 && item.valid == 1) || []; // 过滤只要血压
        const armLeft: any = [], armRight: any = []
        allData.forEach((item: any) => {
            if (item.arm === 1) {
                armLeft.push(item)
            }else {
                armRight.push(item)
            }
        });

        // 获取均值
        const getMean = (list: any, attr: string) => {
            return Math.round(list.reduce((acc: number, curr: any) => acc + curr[attr], 0) / list.length) || 0;
        }

        layer.left = {
            arm: '左手臂',
            sbp: getMean(armLeft, 'sbp'),
            dbp: getMean(armLeft, 'dbp'),
            pulse: getMean(armLeft, 'pulse')
        }
        layer.right = {
            arm: '右手臂',
            sbp: getMean(armRight, 'sbp'),
            dbp: getMean(armRight, 'dbp'),
            pulse: getMean(armRight, 'pulse')
        }
        const recommend = (layer.right.sbp - layer.left.sbp) >= 10 ? 2 : 1
        layer.diff = {
            arm: '压差',
            sbp: Math.abs(layer.left.sbp - layer.right.sbp),
            dbp: Math.abs(layer.left.dbp - layer.right.dbp),
            pulse: Math.abs(layer.left.pulse - layer.right.pulse),
            recommend
        }

        console.log(layer)
    })
}

const changeArm = (item: any) => {
    updateArm({patientId: props.patient.patientId, arm: item.itemValue}).then((res: any) => {
        if (res.success) {
            formData.arm = item.itemValue
            emits('changeArm', formData.arm)
            uni.showToast({
                title: `修改${item.itemName}成功！`,
                icon: 'none'
            })
            return
        }  
    })
}


// 臂围
const armGirthOption: number[][] = [
    [],
]
const pickerArmGirth = ref(false)

const onArmGirth = (item: any) => {
    formData.armGirth = item.value[0]
    pickerArmGirth.value = false


    defIdxs.armGirth = item.indexs[0]
}

for (let index = 0; index < 50; index++) {
    armGirthOption[0].push((20+index))
}

// 袖带
const cuffSOption: Array<Array<{value: number, name: string}>> = [
    options.cuffType,
    options.cuffSize
]
const pickerCuff = ref(false)

const onCuff = (item: any) => {
    console.log(item)
    formData.cuffType = item.value[0].value
    formData.cuffSize = item.value[1].value
    pickerCuff.value = false

    defIdxs.cuffType = item.indexs[0]
    defIdxs.cuffSize = item.indexs[1]

}


// 推荐结果
const showRecommend = ref(false)


const onClickSure = () => {
    if (!armList.value.length) {
        return
    }
    showRecommend.value = true
}

// 提交手臂压差接口
const onClickSubmit = (arm: number) => {

    formData.arm = arm
    let executeStatus = 30
    // 有体位压差单
    if (props.patient.positionDiffMeasure == 1) {
        executeStatus = 20
    }

    const params = Object.assign({}, formData, { armDiffMeasure: layer.diff.sbp, patientId: props.patient.patientId, executeStatus })
    updateMeasureParams(params).then(() => {
        props.patient.executeStatus = executeStatus
    }).finally(() => {
        showRecommend.value = false
    })
}

// 重测压差
const onReset = () => {
    uni.showModal({
        title: '提示',
        content: '是否重新测量双臂压差？',
        success: (res) => {
            if (res.confirm) {
                updateMeasureStatus({ measureStatus: 10, patientId: props.patient.patientId }).then(() => {
                    props.patient.executeStatus = 10
                })
            }
        }
    })
}


// 修改测量数据
const showEditDialog = ref(false)
const editForm: any = ref({})

const onEditItem = (item: any, index: number) => {
    editForm.value = deepClone(item)
    editForm.value.index = index

    editForm.value.valid = '' + item.valid
    editForm.value.arm = '' + item.arm

    console.log(options.valid)

    showEditDialog.value = true
}
const onSaveEdit = async () => {
    try {
        // 调用保存接口
        await editMeasureRecord(editForm.value)
        
        // 更新本地数据
        if (editForm.value.index >= 0) {
            armList.value[editForm.value.index] = { ...editForm.value }
        }
        
        uni.showToast({ title: '保存成功' })
        showEditDialog.value = false
        queryList() // 重新获取最新数据
    } catch (e) {
        uni.showToast({ title: '保存失败', icon: 'error' })
    }
}

onMounted(() => {
    queryList()
    const { arm = 1, armGirth = 32, cuffSize = 1, cuffType = 1 } = props.patient

    // 赋默认值
    formData.arm = arm ?? 1;
    formData.armGirth = armGirth ?? 32;
    formData.cuffSize = cuffSize ?? 1;
    formData.cuffType = cuffType ?? 1;

    defIdxs.armGirth = armGirthOption[0].indexOf(props.patient.armGirth)
    defIdxs.cuffSize = options.cuffSize.findIndex((item: any) => item.value == formData.cuffSize)
    defIdxs.cuffType = options.cuffSize.findIndex((item: any) => item.value == formData.cuffType)
})


defineExpose({
    queryList
})

</script>

<style scoped lang="scss">
.top-filter {
    position: relative;
    width: 100%;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-around;
    background: #fff;
    padding: 20rpx;
    border-radius: 24rpx;
    flex: 0;
    margin: 30rpx 0 20rpx;
    box-sizing: border-box;
    color: #757575;
    // box-shadow: 0px 3px 4px 1px #eee;

    .filter-box {
        position: relative;

        width: auto;
        display: flex;
        flex-direction: column;
        justify-content: space-evenly;

        align-items: center;
        overflow: visible;
        flex: 1;
        &.highlight {
            .up {
                background: #1296db;
                .t {
                    color: white;
                }
            }
        }
        .up {
            width: 100%;
            padding: 16rpx 0;
            position: relative;
            border-radius: 40rpx;
            text-align: center;
            .t {
                font-size: 34rpx;

            }
        }

        .down {
            position: relative;
            .t {
                font-size: 40rpx;

            }
        }
    }
}
</style>