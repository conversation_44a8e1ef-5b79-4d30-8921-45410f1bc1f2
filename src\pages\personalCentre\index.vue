<template>
	<view>
        <BarHeight style="background: #237FC2;"></BarHeight>
		<view class="bg-[#237FC2] h-[500rpx] boxBackground">
			<text class="text-[#fff] flex justify-center pt-[30rpx] font-700">个人中心</text>
			<view class="mt-[50rpx]">
				<uni-card>
					<view class="grid grid-cols-4">
						<view class="grid col-span-3 mt-[20rpx] gap-[10rpx] h-[140rpx]">
							<view class="text-[#014777] text-lg font-700">{{ userInfos?.userName}}，你好</view>
							<!-- <view class="text-[28rpx]">完善个人电子健康档案</view> -->
						</view>
					</view>
				</uni-card>
				<uni-card>
					<view class="relative list-border flex flex-row items-center bg-white rounded-[16rpx] mt-[8rpx] py-[30rpx] px-[40rpx]" v-for="(item, index) in items" :key="index"
                     @click="onClickItem(item)">
						<uni-icons :custom-prefix="item.icon.includes('icon') ? 'iconfont' : ''" :type="item.icon" size="23" color="#999"></uni-icons>
					
						<view class="flex flex-col flex-1 pl-[20rpx]">
							<text class="text-lg text-[#333]">{{ item.name }}</text>
						</view>
					
						<view class="flex flex-row items-center">
							<uni-icons type="right" size="23" color="#014777"></uni-icons>
						</view>
					</view>
					<!-- <view class="flex gap-[10rpx] items-center">
						<view class="w-[6rpx] h-[35rpx] bg-[#005bea] mt-[3rpx]"></view>
						<text class="font-600 text-lg">常用功能</text>
					</view>
					<view class="grid grid-cols-3 gap-3 mt-[20rpx] justify-items-center">
						<view v-for="(item, index) in items" :key="index" class="grid justify-items-center mb-[30rpx]"
							@click="handle(item.link)">
							<uni-icons  custom-prefix="iconfont" :type="item.icon" size="30" color="#2B97E5"></uni-icons>
							<view class="text-base">{{ item.name }}</view>
						</view>
					</view> -->
				</uni-card>
                <uni-card style="margin-top: 50rpx;">
                    <view class="relative flex flex-row items-center bg-white rounded-[16rpx] mt-[8rpx] py-[20rpx] px-[40rpx]" @click="onClickItem('setList')">
						<uni-icons type="list" size="23" color="#999"></uni-icons>
					
						<view class="flex flex-col flex-1 pl-[20rpx]">
							<text class="text-lg text-[#333]">通用设置</text>
						</view>
					
						<view class="flex flex-row items-center">
							<uni-icons type="right" size="23" color="#014777"></uni-icons>
						</view>
					</view>
                </uni-card>
                <view class="w-full h-30rpx"></view>
			</view>
		</view>
	</view>
</template>

<script setup lang="ts">
    import BarHeight from '@/components/BarHeight.vue';

	import { storeToRefs } from 'pinia'
	import { useUserInfo } from '@/stores/userInfo'

	/* import electronicHealthCardIcon from "@/static/clinic-service-icons/electronic-health-card.png";
	import registrationRecordGreenIcon from "@/static/clinic-service-icons/registration-record-green.png";
	import reportQueryIcon from "@/static/clinic-service-icons/report-query.png";
	import outpatientPaymentGreenIcon from "@/static/clinic-service-icons/outpatient-payment-green.png";
	import medicalInsuranceCertificateIcon from "@/static/clinic-service-icons/medical-insurance-certificate.png";
	import currentMeterIcon from "@/static/clinic-service-icons/current-meter.png"; */


	const items = [
		// { icon: "icon-jiankangdangan1", name: "个人档案", link: "/pageInfo/baseInfo"},
		{ icon: "map", name: "设备管理", link: "/pageUseInfo/useDeviceList" },
		{ icon: "map", name: "使用手册", link: "/pageUseInfo/useManual" },
		// { icon: "list", name: "历史档案", link: "/pageUseInfo/historicalArchives" },
		{ icon: "map-filled", name: "隐私条款", link: "/pageUseInfo/usePolicy" },
		// { icon: "settings-filled", name: "通用设置", link: "" },
	];

    function onClickItem (item: any) {
        if (item === 'setList') {
            uni.navigateTo({
                url: '/pageUseInfo/setList',
                animationType: "slide-in-right",
                animationDuration: 300,
            })
            return
        }

        item.link && uni.navigateTo({
            url: item.link,
            animationType: "slide-in-right",
            animationDuration: 300,
        })
    }

    const handleAddMenber =()=>{
        uni.navigateTo({
            url: "/pageFamily/addFamilyMenber",
            animationType: "slide-in-right",
            animationDuration: 300,
        });
    }
        const storeUserInfo = useUserInfo();
        const { userInfos } = storeToRefs(storeUserInfo);


	const handleBtn = () => {
		uni.navigateTo({
			url: "/pageInfo/baseInfo",
			animationType: "slide-in-right",
			animationDuration: 300,
		});
	};

</script>


<style scoped lang="scss">
	.list-border:after {
		position: absolute;
		bottom: 0;
		right: 0;
		left: 0;
		height: 1px;
		content: "";
		-webkit-transform: scaleY(.5);
		transform: scaleY(.5);
		background-color: #bbb;
	}
    .list-border:last-child::after{
        display: none;
    }
	::v-deep .uni-card {
		border-radius: 10px;
	}

	.signout-button {
		height: 68rpx;
		line-height: 68rpx;
		border-radius: 16rpx;
		background-color: #ff3333;
		color: #fff;
		text-align: center;
		margin: 28rpx;
	}
</style>