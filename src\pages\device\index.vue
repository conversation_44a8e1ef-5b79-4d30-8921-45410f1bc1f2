<template>
	<view class="p-[30rpx]">
        <view class="w-full">
            <view v-for="item in dataList" :key="item.deviceId" class="mb-[40rpx]">
                <!-- 设备屏幕 -->
                <DeviceBoot :dataMap="item" @changeDevice="changeDevice"></DeviceBoot>
            </view>
            <view v-if="dataList.length === 0" class="flex flex-col items-center justify-center bg-white mb-[20rpx] pb-[20px]">
                <image class="h-[200rpx] pt-5 pb-5" src="/static/img/not_device.png" mode="aspectFit"></image>
                <span class="text-[#999999]">无设备</span>
            </view>
        </view>
        <!-- 添加设备 -->
        <view @click="onclickAddDevice" class="w-full h-[120rpx] flex items-center justify-center bg-white rounded-[16rpx] mb-[80rpx]">添加设备</view>
	
        <PreDevice></PreDevice>

        <view class="h-[100rpx]"></view>
    </view>
</template>

<script setup lang="ts">
    import { queryUserDevice, unbindDevice } from '@/common/api/device';
    import DeviceBoot from './components/deviceBoot.vue';
    import PreDevice from '@/components/PreDevice.vue';
    
	import { onMounted, ref } from "vue";
	import { onLoad, onShow, onHide } from "@dcloudio/uni-app";
    import { useUserInfo } from '@/stores/userInfo'
	const storeUserInfo: any = useUserInfo();
    const dataList = ref<any[]>([]);
    // 加载
    const timer: any = ref(null)
    const outoPoll = ref(false)

    const queryList = () => {
        queryUserDevice({condition: {
            userId: storeUserInfo.areaId,
            deviceType: 'HYO2'
        }}).then((res : any) => {
            let data = res.data.list || []
            console.log(data)
            dataList.value = data
        }).finally(() => {
            clearTimeout(timer.value)

            if (!outoPoll.value) {
                return
            }
            timer.value = setTimeout(() => {
                queryList()
            }, 1000 * 1.5);
        })
    }

    const changeDevice = (data : any) => {
        // 提示是否解绑
        uni.showModal({
            title: '提示',
            content: '是否解绑设备?',
            success: (res) => {
                if (res.confirm) {
                    unbindDevice({ deviceNo: data.deviceNo, userId: storeUserInfo.userId }).then((res: any) => {
                        // 提示绑定成功
                        uni.showToast({
                            title: '解绑成功',
                            icon: 'success'
                        })
                    })
                }
            }
        })
    }

    const onclickAddDevice = () => {
        uni.navigateTo({
            url: '/pageDevice/addDevice'
        })
    }

	onShow(() => {
        outoPoll.value = true
        queryList()
	});
    onHide(() => {
        clearTimeout(timer.value)
        outoPoll.value = false
    })

</script>

<style scoped lang="scss">

</style>