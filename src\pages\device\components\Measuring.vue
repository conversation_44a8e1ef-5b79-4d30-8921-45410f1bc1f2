<template>
    <view class="measurement">
        <view class="left" :class="props.dataMap.groupCode">
            <view class="content">
                <template v-if="'HYO2' === props.dataMap.groupCode">
                    <!-- 血压血氧 -->
                    <!-- 血压 -->
                    <view class="row" style="border-top: none;">
                        <view class="item" style="flex: 8">
                            <view class="item-label">
                                <view class="value">血压</view>
                                <view class="desc">mmHg</view>
                            </view>
                            <view class="item-value">{{ sbpdpb }}</view>
                        </view>
                        <view class="item" style="flex: 6">
                            <view class="item-label">
                                <view class="value">脉搏</view>
                                <view class="desc">bpm</view>
                                <!-- <text v-if="showIcon" :class="{blink: blink}" class="icon-status iconfont icon-24gf-heartPulse"></text> -->
                            </view>
                            <view class="item-value">{{ props.curMeasuring.pulse || '' }}</view>
                        </view>
                    </view>
                    <view class="row">
                        <!-- 血氧 -->
                        <view class="item" style="flex: 8">
                            <view class="item-label">
                                <view class="value">血氧</view>
                                <view class="desc">%SpO2</view>
                                <!-- <text v-if="showIcon" :class="{blink: blink}" class="icon-status iconfont icon-xieyang"></text> -->
                            </view>
                            <view class="item-value">{{ props.curMeasuring.spoz || '' }}</view>
                        </view>
                        <view class="item" style="flex: 6">
                            <view class="item-label">
                                <view class="value">脉率</view>
                                <view class="desc">PRbpm</view>
                            </view>
                            <view class="item-value">{{ props.curMeasuring.hr || '' }}</view>
                        </view>
                    </view>

                </template>
                <template v-else-if="'SPHY' === props.dataMap.groupCode">
                    <!-- 血压 -->
                    <view class="item">
                        <view class="item-label">
                            <view class="value">高压</view>
                            <view class="desc">mmHg</view>
                        </view>
                        <view class="item-value">{{ sbpValue }}</view>
                    </view>
                    <view class="item">
                        <view class="item-label">
                            <view class="value">低压</view>
                            <view class="desc">mmHg</view>
                        </view>
                        <view class="item-value">{{ props.curMeasuring.dpb || '' }}</view>
                    </view>
                    <view class="item">
                        <view class="item-label">
                            <view class="value">脉搏</view>
                            <view class="desc">bpm</view>
                            <!-- <text v-if="showIcon" :class="{blink: blink}" class="icon-status iconfont icon-24gf-heartPulse"></text> -->
                        </view>
                        <view class="item-value">{{ props.curMeasuring.pulse || '' }}</view>
                    </view>
                </template>
                <template v-else-if="'SPO2' === props.dataMap.groupCode">
                    <!-- 血氧 -->
                    <view class="item">
                        <view class="item-label">
                            <view class="value">血氧</view>
                            <view class="desc">%SpO2</view>
                            <!-- <text v-if="showIcon" :class="{blink: blink}" class="icon-status iconfont icon-xieyang"></text> -->
                        </view>
                        <view class="item-value">{{ props.curMeasuring.spoz || '' }}</view>
                    </view>
                    <view class="item">
                        <view class="item-label">
                            <view class="value">脉率</view>
                            <view class="desc">PRbpm</view>
                        </view>
                        <view class="item-value">{{ props.curMeasuring.hr || '' }}</view>
                    </view>
                </template>
                <template v-else-if="'THERMOMETER' === props.dataMap.groupCode || 'THERMOMETERSPLINCHED' === props.dataMap.groupCode">
                    <!-- 体温 -->
                    <view class="item">
                        <view class="item-label">
                            <view class="value">体温</view>
                            <view class="desc" style="font-size: 26rpx;">°C</view>
                        </view>
                        <view class="item-value">{{ tempValue }}</view>
                    </view>
                    <view style="height: 24rpx;"></view>
                </template>
                <view class="item item-date" :style="{'marginTop': 'SPHY' === props.dataMap.groupCode ? '0' : '24rpx'}">
                    <view class="item-label">测量时间</view>
                    <view class="item-value">{{ measureTime }}</view>
                </view>

                <view class="tip" v-if="props.curMeasuring.measureStatus === 0">
                    <view class="label">测量失败</view>
                    <view>{{ curMeasuring.measureMsg }}</view>
                    <view class="label" style="margin-top: 20rpx;">{{ measureTime }}</view>
                </view>
            </view>
            <!-- <view class="btn-box"> -->
                <slot></slot>
            <!-- </view> -->
        </view>
        <view class="right">
            <!-- 血氧 -->
            <AnimationHeart v-if="props.dataMap.measureStatus === 1" :animationHr="animationHr"></AnimationHeart>
            <!-- 血压 -->
            <AnimationBloodPressure v-if="props.dataMap.measureStatus === 3" :curMeasuring="props.curMeasuring"></AnimationBloodPressure>
        </view>
    </view>
</template>
<script setup>
    import moment from 'moment';
    import AnimationHeart from '@/components/animation/AnimationHeart.vue'; // 血氧出值时心率跳动
    import AnimationBloodPressure from '@/components/animation/AnimationBloodPressure.vue';
    import { ref, reactive, computed, defineProps, defineEmits } from 'vue'

    const props = defineProps({
        dataMap: Object,
        curMeasuring: Object
    })


    // 血压
    const sbpValue = computed(() => {
        if (props.curMeasuring.measureStatus === 2) {
            return props.curMeasuring.currentBp
        }
        return props.curMeasuring.sbp
    })
    const sbpdpb = computed(() => {
        if (props.curMeasuring.dpb && sbpValue.value) {
            return `${sbpValue.value}/${props.curMeasuring.dpb}`
        }else if (sbpValue.value) {
            return sbpValue.value
        }else {
            return ''
        }
    })

    // 体温
    const tempValue = computed(() => {
        if (props.curMeasuring.measureStatus === 2) {
            return props.curMeasuring.currentTemp
        }
        return props.curMeasuring.temp
    })

    // 测量时间
    const measureTime = computed(() => {
        const res = props.curMeasuring.measureTime
        if (!res) {
            return ''
        }
        return moment(res).format('HH:mm')
    })


    // 动画
    // 血氧动画
    const animationHr = computed(() => {
        if (props.dataMap.measureStatus === 1) {
            // const values = [120, 50, 70, 0.01];
            // // 随机选择一个值
            // const randomValue = values[Math.floor(Math.random() * values.length)];

            return props.curMeasuring.hr || 60 // randomValue
        }
        return 60
    })

    // 是否显示跳动图标
    const showIcon = computed(() => {
        return props.curMeasuring.measureStatus === 2 || props.curMeasuring.measureStatus === 1
    })

    // 是否闪烁
    const blink = computed(() => {
        return props.curMeasuring.measureStatus === 2
    })
</script>
<style lang="scss" scoped>
.measurement {
    display: flex;
    height: 100%;
    flex: 1;
    overflow: hidden;
    padding-bottom: 10rpx;

    .left {
        flex: 1;
        overflow: hidden;
        display: flex;
        justify-content: center;
        flex-direction: column;
        &.SPHY, &.SPO2, &.THERMOMETER, &.THERMOMETERSPLINCHED {
            width: 80%;
            flex: none;
        }
        &.HYO2 {
            .row {
                padding-right: 12rpx;
            }
            .item {
                padding-right: 12rpx;
                .item-value {
                    font-size: 40rpx;
                    font-weight: bold;
                    flex: 1;
                    text-align: center;
                }
            }
            .item-date {
                width: 84%;
            }
        }
        .content {
            position: relative;
            .tip {
                position: absolute;
                left: 0;
                top: 0;
                width: 100%;
                height: 100%;
                background: $xx-color-primary;
                text-align: center;
                display: flex;
                flex-direction: column;
                justify-content: center;
                font-size: 34rpx;
                color: #ff9900;
                .label {
                    font-size: 28rpx;
                    padding-bottom: 14rpx;
                }
            }
        }
    }
    .right {
        display: flex;
        align-items: center;
    }
    .row {
        border-top: 1px solid rgba(7, 129, 217, 1);
        display: flex;
        .item {
            border-top: none;
        }
    }
    .item {
        display: flex;
        flex-direction: row;
        align-items: center;
        // justify-content: space-between;
        height: 80rpx;
        width: 100%;
        border-top: 1px solid rgba(7, 129, 217, 1);
        &:first-child {
            border-top: none;
        }
        .item-label {
            font-size: 32rpx;
            position: relative;
            top: 4rpx;
            .value {
                color: rgba(255, 255, 255, 1);
            }
            .desc {
                font-size: 22rpx;
                color: rgba(228, 242, 247, 1);
                position: relative;
                top: -6rpx;
            }
        }
        .item-value {
            font-size: 68rpx;
            color: rgba(51, 223, 238, 1);
            padding-left: 14rpx;
            font-weight: bold;
            flex: 1;
            text-align: center;
        }   
        &.item-row {
            .item-label {
                display: flex;
                flex-direction: row;
            }
            // &:last-child {
            //     border-bottom: 1px solid rgba(20, 117, 186, 1) !important;
            // }
        }
        &.thermometer, &.THERMOMETERSPLINCHED {
            margin-top: 80rpx;
        }
    }
    .icon-status {
        position: absolute;
        right: -26rpx;
        top: 6rpx;
        // css 闪烁显示
        &.blink {
            animation: blink 1s infinite;
        }
    }
    .item-date {
        height: 74rpx;
        border-top: none;
        .item-label {
            font-size: 30rpx;
            top: 0;
        }
        .item-value {
            color: white;
            font-size: 40rpx;
            justify-content: start;
            padding-left: 14rpx;
            color: #33DFEE;
            padding-left: 0;
            position: relative;
            left: -16rpx;
        }
    }
    .btn-box {
        height: 52rpx;
    }
}
</style>