<template>
  <view class="unified-bluetooth">
    <view class="status-section">
      <text>蓝牙状态: {{ isAvailable ? '可用' : '不可用' }}</text>
      <text>连接状态: {{ isConnected ? '已连接' : '未连接' }}</text>
      <text>当前设备: {{ connectedDevice?.name || '无' }}</text>
    </view>
    
    <!-- 实时数据显示 -->
    <view class="data-section">
      <text class="section-title">实时数据</text>
      <view v-for="(item, index) in realtimeData" :key="index" class="data-item">
        <view class="data-header">
          <text class="device-name">{{ item.deviceName }}</text>
          <text class="packet-type">{{ item.包类型 }}</text>
          <text class="data-length">长度: {{ item.长度 }}</text>
        </view>
        <view class="data-content">
          <text>{{ formatDataContent(item.data) }}</text>
        </view>
        <view class="timestamp">
          <text>{{ formatTime(item.timestamp) }}</text>
        </view>
      </view>
    </view>
    
    <!-- 统计信息 -->
    <view class="stats-section">
      <text class="section-title">数据统计</text>
      <view class="stats-grid">
        <view class="stat-item">
          <text class="stat-label">测量结果</text>
          <text class="stat-value">{{ stats.测量结果 }}</text>
        </view>
        <view class="stat-item">
          <text class="stat-label">袖带压</text>
          <text class="stat-value">{{ stats.袖带压 }}</text>
        </view>
        <view class="stat-item">
          <text class="stat-label">电池信息</text>
          <text class="stat-value">{{ stats.电池信息 }}</text>
        </view>
        <view class="stat-item">
          <text class="stat-label">其他数据</text>
          <text class="stat-value">{{ stats.其他 }}</text>
        </view>
      </view>
    </view>
    
    <!-- 操作按钮 -->
    <view class="action-section">
      <button @click="startMeasurement" :disabled="!isConnected">开始测量</button>
      <button @click="clearData">清除数据</button>
      <button @click="exportData">导出数据</button>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, computed } from 'vue';
import { useBlueToothData } from '@/common/useBlueTooth';

// 使用蓝牙数据
const {
  // 状态
  state,
  isAvailable,
  isConnected,
  connectedDevice,
  
  // 统一回调方法
  onBluetoothData,
  clearAllCallbacks,
  
  // 功能方法
  pressure_startMeasurement
} = useBlueToothData();

// 实时数据存储
const realtimeData = ref<Array<{
  deviceName: string;
  包类型: string;
  长度: number;
  data: any;
  timestamp: number;
}>>([]);

// 数据统计
const stats = ref({
  测量结果: 0,
  袖带压: 0,
  电池信息: 0,
  启动测量回复: 0,
  时间数据: 0,
  其他: 0
});

// 取消回调的函数
let unsubscribeBluetoothData: (() => void) | null = null;

// 格式化数据内容
const formatDataContent = (data: any) => {
  if (typeof data === 'object') {
    return JSON.stringify(data, null, 2);
  }
  return String(data);
};

// 格式化时间
const formatTime = (timestamp: number) => {
  return new Date(timestamp).toLocaleTimeString();
};

// 开始测量
const startMeasurement = async () => {
  try {
    await pressure_startMeasurement();
    uni.showToast({
      title: '开始测量',
      icon: 'success'
    });
  } catch (error) {
    uni.showToast({
      title: '测量失败',
      icon: 'error'
    });
  }
};

// 清除数据
const clearData = () => {
  realtimeData.value = [];
  stats.value = {
    测量结果: 0,
    袖带压: 0,
    电池信息: 0,
    启动测量回复: 0,
    时间数据: 0,
    其他: 0
  };
  uni.showToast({
    title: '数据已清除',
    icon: 'success'
  });
};

// 导出数据
const exportData = () => {
  const exportContent = {
    统计: stats.value,
    数据: realtimeData.value
  };
  
  console.log('导出数据:', exportContent);
  uni.showToast({
    title: '数据已导出到控制台',
    icon: 'success'
  });
};

onMounted(() => {
  // 注册统一蓝牙数据回调
  unsubscribeBluetoothData = onBluetoothData((callbackData) => {
    console.log('收到蓝牙数据:', callbackData);
    
    // 添加时间戳
    const dataWithTimestamp = {
      ...callbackData,
      timestamp: Date.now()
    };
    
    // 添加到实时数据（最多保留50条）
    realtimeData.value.unshift(dataWithTimestamp);
    if (realtimeData.value.length > 50) {
      realtimeData.value = realtimeData.value.slice(0, 50);
    }
    
    // 更新统计
    const 包类型 = callbackData.包类型;
    if (stats.value.hasOwnProperty(包类型)) {
      stats.value[包类型 as keyof typeof stats.value]++;
    } else {
      stats.value.其他++;
    }
    
    // 根据数据类型显示不同的通知
    switch (包类型) {
      case '测量结果':
        const result = callbackData.data;
        uni.showToast({
          title: `测量完成: ${result.systolic}/${result.diastolic}`,
          icon: 'success'
        });
        break;
      case '袖带压':
        // 袖带压数据通常很频繁，不显示通知
        break;
      case '电池信息':
        const battery = callbackData.data;
        if (battery.percentage < 20) {
          uni.showToast({
            title: `电量低: ${battery.percentage}%`,
            icon: 'error'
          });
        }
        break;
    }
  });
});

onUnmounted(() => {
  // 取消回调
  if (unsubscribeBluetoothData) {
    unsubscribeBluetoothData();
  }
});
</script>

<style scoped>
.unified-bluetooth {
  padding: 20rpx;
}

.status-section,
.data-section,
.stats-section,
.action-section {
  margin-bottom: 40rpx;
  padding: 20rpx;
  border: 1px solid #eee;
  border-radius: 10rpx;
  background-color: #fff;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
  color: #333;
}

.data-item {
  margin-bottom: 20rpx;
  padding: 15rpx;
  background-color: #f8f8f8;
  border-radius: 8rpx;
  border-left: 4rpx solid #007aff;
}

.data-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10rpx;
}

.device-name {
  font-weight: bold;
  color: #007aff;
}

.packet-type {
  background-color: #007aff;
  color: white;
  padding: 4rpx 8rpx;
  border-radius: 4rpx;
  font-size: 24rpx;
}

.data-length {
  font-size: 24rpx;
  color: #666;
}

.data-content {
  margin-bottom: 10rpx;
  font-family: monospace;
  font-size: 24rpx;
  background-color: #fff;
  padding: 10rpx;
  border-radius: 4rpx;
}

.timestamp {
  text-align: right;
  font-size: 22rpx;
  color: #999;
}

.stats-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20rpx;
}

.stat-item {
  text-align: center;
  padding: 20rpx;
  background-color: #f0f0f0;
  border-radius: 8rpx;
}

.stat-label {
  display: block;
  font-size: 24rpx;
  color: #666;
  margin-bottom: 10rpx;
}

.stat-value {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  color: #007aff;
}

button {
  padding: 20rpx 40rpx;
  margin: 10rpx;
  border: none;
  border-radius: 10rpx;
  background-color: #007aff;
  color: white;
  font-size: 28rpx;
}

button:disabled {
  background-color: #ccc;
}
</style>
