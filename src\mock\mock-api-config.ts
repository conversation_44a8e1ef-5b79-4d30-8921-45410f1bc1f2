 
// 定义接口描述mockBeans数组中的对象结构
interface MockApi {
	apiName : string;
	method : string;
	mockFile : string;
}

// 定义mockBeans数组类型
const mockBeans : MockApi[] = [
	{
		apiName: 'rest/global/getMMSCode', // 获取验证码
		method: 'get',
		mockFile: 'sample.json'
	},
	{
		apiName: 'rest/user/checkEmployee', // 验证/校验工号及密码
		method: 'post',
		mockFile: 'sample.json'
	},
	{
		apiName: 'rest/measure/lastRecord', //（根据主指标类型）获取最近一次相关监测数据
		method: 'get',
		mockFile: 'healthMeasure.json'
	}
];

function isMockApi(apiName : string, method : string) : boolean {
	// 判断url是不是链接
	let urlType = /^([hH][tT]{2}[pP]:\/\/|[hH][tT]{2}[pP][sS]:\/\/)(([A-Za-z0-9-~]+).)+([A-Za-z0-9-~/])+$/.test(apiName);
	let result = false;
	for (let i = 0; i < mockBeans.length; i++) {
		if (urlType) {
			if (apiName.endsWith(mockBeans[i].apiName) && method.toLowerCase() === mockBeans[i].method.toLowerCase()) {
				result = true;
				break;
			}
		} else {
			if (apiName === mockBeans[i].apiName && method.toLowerCase() === mockBeans[i].method.toLowerCase()) {
				result = true;
				break;
			}
		}
	}
	return result;
}

function getMockResponse(apiName : string, method : string) {
	// 判断url是不是链接
	let urlType = /^([hH][tT]{2}[pP]:\/\/|[hH][tT]{2}[pP][sS]:\/\/)(([A-Za-z0-9-~]+).)+([A-Za-z0-9-~/])+$/.test(apiName);
	let mockFilePath = '';
	for (let i = 0; i < mockBeans.length; i++) {
		if (urlType) {
			if (apiName.endsWith(mockBeans[i].apiName) && method.toLowerCase() === mockBeans[i].method.toLowerCase()) {
				mockFilePath = mockBeans[i].mockFile;
				break;
			}
		} else {
			if (apiName === mockBeans[i].apiName && method.toLowerCase() === mockBeans[i].method.toLowerCase()) {
				mockFilePath = mockBeans[i].mockFile;
				break;
			}
		}
	}

	console.log('mockFilePath=' + mockFilePath);
	let jsonResult = require(`mock/${mockFilePath}`);

	console.warn("jsonResult", jsonResult);
	return jsonResult;
}

/* const getJSON = (filePath) => {
	return new Promise((resolve, reject) => {
		plus.io.resolveLocalFileSystemURL(filePath, entry => {
			entry.file(file => {
				const fileReader = new plus.io.FileReader();
				fileReader.onloadend = evt => {
					const data = JSON.parse(evt.target.result);
					resolve(data);
				};
				fileReader.readAsText(file, 'utf-8');
				fileReader.onerror = error => {
					console.log("读取JSON--失败2")
					reject(false); // 失败时拒绝Promise
				};
			});
		}, error => {
			console.log("读取JSON--失败1")
			reject(false); // 文件系统URL解析失败时拒绝Promise
		});
	});
} */
export default { isMockApi, getMockResponse }