<template>
    <view class="ecg-container">
        <view class="ecg-line" :style="{
            animationDuration: animationDuration + 's',
            '--animation-duration': animationDuration + 's'
        }">
            <!-- 基线 -->
            <view class="ecg-baseline"></view>
            <!-- 心电图波形 -->
            <view class="ecg-wave ecg-wave-1"></view>
            <view class="ecg-wave ecg-wave-2"></view>
            <view class="ecg-wave ecg-wave-3"></view>
        </view>
        <!-- 左右边缘渐变遮罩 -->
        <view class="fade-overlay fade-left"></view>
        <view class="fade-overlay fade-right"></view>
    </view>
</template>

<script setup>
import { computed } from 'vue';

const props = defineProps({
    animationHr: {
        type: Number,
        default: 20
    }
});

// 根据心率计算动画持续时间
const animationDuration = computed(() => {
    // 心率越高，动画越快
    return 60 / props.animationHr;
});
</script>

<style lang="scss" scoped>
.ecg-container {
    width: 100%;
    height: 60rpx;
    overflow: hidden;
    position: relative;
    background: transparent;
}

.ecg-line {
    width: 100%;
    height: 100%;
    position: relative;
    overflow: hidden;
}

.ecg-baseline {
    position: absolute;
    top: 50%;
    left: 0;
    width: 200%;
    height: 2rpx;
    background: #33DFEE;
    transform: translateY(-50%);
    animation: ecg-scroll var(--animation-duration, 1s) linear infinite;
    opacity: 0.4;
}

.ecg-wave {
    position: absolute;
    top: 0;
    left: 0;
    width: 200%;
    height: 100%;
    animation: ecg-scroll var(--animation-duration, 1s) linear infinite;
    filter: drop-shadow(0 0 4rpx rgba(51, 223, 238, 0.8));
}

.ecg-wave-1 {
    background:
        // 心电图路径模拟 - 使用多个小的矩形连接
        linear-gradient(90deg,
            transparent 0%, transparent 5%,
            #33DFEE 5%, #33DFEE 5.2%, transparent 5.2%, transparent 6%,
            #33DFEE 6%, #33DFEE 6.2%, transparent 6.2%, transparent 7%,
            #33DFEE 7%, #33DFEE 7.2%, transparent 7.2%, transparent 8%,
            #33DFEE 8%, #33DFEE 8.2%, transparent 8.2%, transparent 9%,
            #33DFEE 9%, #33DFEE 9.2%, transparent 9.2%, transparent 10%,
            #33DFEE 10%, #33DFEE 10.2%, transparent 10.2%, transparent 11%,
            #33DFEE 11%, #33DFEE 11.2%, transparent 11.2%, transparent 12%,
            #33DFEE 12%, #33DFEE 12.2%, transparent 12.2%, transparent 13%,
            #33DFEE 13%, #33DFEE 13.2%, transparent 13.2%, transparent 14%,
            #33DFEE 14%, #33DFEE 14.2%, transparent 14.2%, transparent 15%,
            #33DFEE 15%, #33DFEE 15.2%, transparent 15.2%, transparent 16%,
            #33DFEE 16%, #33DFEE 16.2%, transparent 16.2%, transparent 17%,
            #33DFEE 17%, #33DFEE 17.2%, transparent 17.2%, transparent 18%,
            #33DFEE 18%, #33DFEE 18.2%, transparent 18.2%, transparent 19%,
            #33DFEE 19%, #33DFEE 19.2%, transparent 19.2%, transparent 20%,
            #33DFEE 20%, #33DFEE 20.2%, transparent 20.2%, transparent 21%,
            #33DFEE 21%, #33DFEE 21.2%, transparent 21.2%, transparent 22%,
            #33DFEE 22%, #33DFEE 22.2%, transparent 22.2%, transparent 23%,
            #33DFEE 23%, #33DFEE 23.2%, transparent 23.2%, transparent 24%,
            #33DFEE 24%, #33DFEE 24.2%, transparent 24.2%, transparent 25%,
            transparent 25%, transparent 50%
        );
    background-size: 300rpx 2rpx;
    background-repeat: repeat-x;
    background-position: 0 50%;
}

.ecg-wave-2 {
    // P波和T波
    background:
        radial-gradient(ellipse 15rpx 6rpx at 8% 45%, #33DFEE 0%, transparent 70%),
        radial-gradient(ellipse 20rpx 8rpx at 22% 42%, #33DFEE 0%, transparent 70%),
        radial-gradient(ellipse 15rpx 6rpx at 58% 45%, #33DFEE 0%, transparent 70%),
        radial-gradient(ellipse 20rpx 8rpx at 72% 42%, #33DFEE 0%, transparent 70%);
    background-size: 300rpx 60rpx;
    background-repeat: repeat-x;
}

.ecg-wave-3 {
    // QRS波群
    background:
        radial-gradient(ellipse 3rpx 25rpx at 16% 25%, #33DFEE 0%, transparent 70%),
        radial-gradient(ellipse 3rpx 10rpx at 17% 65%, #33DFEE 0%, transparent 70%),
        radial-gradient(ellipse 3rpx 25rpx at 66% 25%, #33DFEE 0%, transparent 70%),
        radial-gradient(ellipse 3rpx 10rpx at 67% 65%, #33DFEE 0%, transparent 70%);
    background-size: 300rpx 60rpx;
    background-repeat: repeat-x;
}

@keyframes ecg-scroll {
    0% {
        transform: translateX(0);
    }
    100% {
        transform: translateX(-400rpx);
    }
}

.fade-overlay {
    position: absolute;
    top: 0;
    height: 100%;
    pointer-events: none;
    z-index: 1;
}

.fade-left {
    left: 0;
    width: 40rpx;
    background: linear-gradient(to right,
        rgba(255, 255, 255, 1) 0%,
        rgba(255, 255, 255, 0.9) 20%,
        rgba(255, 255, 255, 0.6) 50%,
        rgba(255, 255, 255, 0.2) 80%,
        rgba(255, 255, 255, 0) 100%
    );
}

.fade-right {
    right: 0;
    width: 40rpx;
    background: linear-gradient(to left,
        rgba(255, 255, 255, 1) 0%,
        rgba(255, 255, 255, 0.9) 20%,
        rgba(255, 255, 255, 0.6) 50%,
        rgba(255, 255, 255, 0.2) 80%,
        rgba(255, 255, 255, 0) 100%
    );
}
</style>