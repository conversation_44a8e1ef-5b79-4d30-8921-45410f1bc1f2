import registeredIcon from "@/static/clinic-service-icons/registered.png";
import contributoryIcon from "@/static/clinic-service-icons/contributory.png";
import intelligentDiagnosisIcon from "@/static/clinic-service-icons/intelligentDiagnosis.png";
import reportingIcon from "@/static/clinic-service-icons/reporting.png";
import moreFunctionsIcon from "@/static/clinic-service-icons/moreFunctions.png";
import registrationRecordIcon from "@/static/clinic-service-icons/registration-record.png";
import homecareIcon from "@/static/clinic-service-icons/homecare.png";
import waitingListEnquiryIcon from "@/static/clinic-service-icons/waiting-list-enquiry.png";
import medicalInsurancePaymentIcon from "@/static/clinic-service-icons/medical-insurance-payment.png";
import deliverMedicineIcon from "@/static/clinic-service-icons/deliver-medicine.png";
import familyDoctorContractIcon from "@/static/clinic-service-icons/family-doctor-contract.png";
import physicalExaminationGuideIcon from "@/static/clinic-service-icons/physical-examination-guide.png";
import personalMedicalAppointmentIcon from "@/static/clinic-service-icons/personal-medical-appointment.png";
import myAppointmentIcon from "@/static/clinic-service-icons/my-appointment.png";
import questionnaireIcon from "@/static/clinic-service-icons/questionnaire.png";
import physicalExaminationReportIcon from "@/static/clinic-service-icons/physical-examination-report.png";
import unitGroupInspectionIcon from "@/static/clinic-service-icons/unit-group-inspection.png";


// 门诊服务模块
export const indexOutpatientServices = [
  {
    name: "预约/挂号",
    src: registeredIcon,
    backgroundColor: "#8ae7a1",
    link: "",
  },
  {
    name: "门诊缴费",
    src: contributoryIcon,
    backgroundColor: "#f55121",
    link: "",
  },
  {
    name: "家医签约",
    src: reportingIcon,
    backgroundColor: "#f1ae89",
    link: "/family_doctor_signing/index",
  },
  {
    name: "AI问诊",
    src: intelligentDiagnosisIcon,
    backgroundColor: "#ea2620",
    link: "/page_ai_clinic/index",
  },
  {
    name: "挂号记录",
    src: registrationRecordIcon,
    backgroundColor: "#dfa999",
    link: "",
  },
  {
    name: "居家护理",
    src: homecareIcon,
    backgroundColor: "#1ac844",
    link: "",
  },
  {
    name: "送药上门",
    src: deliverMedicineIcon,
    backgroundColor: "#f30b86",
    link: "",
  },
  {
    name: "更多功能",
    src: moreFunctionsIcon,
    backgroundColor: "#0b6af3",
    link: "/pages/moreFunctions/index",
  },
];
//更多
export const outpatientServices = [
  {
    name: "预约/挂号",
    src: registeredIcon,
    backgroundColor: "#8ae7a1",
    link: "",
  },
  {
    name: "门诊缴费",
    src: contributoryIcon,
    backgroundColor: "#f55121",
    link: "",
  },
  {
    name: "报告查询",
    src: reportingIcon,
    backgroundColor: "#f1ae89",
    link: "",
  },
  {
    name: "智能导诊",
    src: intelligentDiagnosisIcon,
    backgroundColor: "#ea2620",
    link: "",
  },
  {
    name: "挂号记录",
    src: registrationRecordIcon,
    backgroundColor: "#dfa999",
    link: "",
  },
  {
    name: "居家护理",
    src: homecareIcon,
    backgroundColor: "#1ac844",
    link: "",
  },
  {
    name: "送药上门",
    src: deliverMedicineIcon,
    backgroundColor: "#f30b86",
    link: "",
  },
  {
    name: "家医签约",
    src: familyDoctorContractIcon,
    backgroundColor: "#fa5900",
    link: "",
  },
  {
    name: "医保支付",
    src: medicalInsurancePaymentIcon,
    backgroundColor: "#0b6af3",
    link: "/pages/moreFunctions/index",
  },
];

// 体检服务模块
export const physicalExamination = [
  {
    name: "体检指南",
    src: physicalExaminationGuideIcon,
    backgroundColor: "#0b6af3",
    link: "",
  },
  {
    name: "个人体检预约",
    src: personalMedicalAppointmentIcon,
    backgroundColor: "#1cc245",
    link: "",
  },
  {
    name: "我的预约",
    src: myAppointmentIcon,
    backgroundColor: "#fa5900",
    link: "",
  },
  {
    name: "体检报告",
    src: physicalExaminationReportIcon,
    backgroundColor: "#f30b86",
    link: "",
  },
  {
    name: "单位团检",
    src: unitGroupInspectionIcon,
    backgroundColor: "#1ac844",
    link: "",
  },
  {
    name: "问卷调查",
    src: questionnaireIcon,
    backgroundColor: "#f1ae89",
    link: "",
  },
];

export const convenienceService = [
  {
    name: "门诊缴费",
    src: contributoryIcon,
    backgroundColor: "#f55121",
    link: "",
  },
  {
    name: "门诊缴费",
    src: contributoryIcon,
    backgroundColor: "#f55121",
    link: "",
  },
  {
    name: "门诊缴费",
    src: contributoryIcon,
    backgroundColor: "#f55121",
    link: "",
  },
  {
    name: "门诊缴费",
    src: contributoryIcon,
    backgroundColor: "#f55121",
    link: "",
  },
  {
    name: "门诊缴费",
    src: contributoryIcon,
    backgroundColor: "#f55121",
    link: "",
  },
  {
    name: "门诊缴费",
    src: contributoryIcon,
    backgroundColor: "#f55121",
    link: "",
  },
  {
    name: "门诊缴费",
    src: contributoryIcon,
    backgroundColor: "#f55121",
    link: "",
  },
  {
    name: "门诊缴费",
    src: contributoryIcon,
    backgroundColor: "#f55121",
    link: "",
  },
];
