## 2.8.3（2024-11-27）
1.`修复` `doInsertVirtualListItem`插入数据无效的问题。  
2.`优化` 提升兼容性&细节优化。  
## 2.8.2（2024-11-25）
1.`优化` types中`ZPagingRef`和`ZPagingInstance`支持泛型。  
## 2.8.1（2024-11-24）
1.`新增` 完整的`props`、`slots`、`methods`、`events`的typescript types声明，可在ts中获得绝佳的代码提示体验。  
2.`新增` `virtual-cell-id-prefix`：虚拟列表cell id的前缀，适用于一个页面有多个虚拟列表的情况，用以区分不同虚拟列表cell的id。  
3.`修复` 在vue3+(微信小程序或QQ小程序)中，使用非内置列表写法时，若`z-paging`在`swiper-item`标签内的情况下存在的无法获取slot插入的cell高度的问题。  
4.`修复` 在虚拟列表中分页数据小于1页时插入新数据，虚拟列表未生效的问题。  
5.`修复` 在虚拟列表中调用`refresh`时，cell的index计算不正确的问题。  
6.`修复` 在快手小程序中内容较少或空数据时`z-paging`未能铺满全屏的问题。  
7.`优化` `events`中的参数涉及枚举的部分，统一由之前的number类型修改为string类型，展示更直观！涉及的events：`@query`中的`from`参数；`@refresherStatusChange`中的`status`参数；`@loadingStatusChange`中的`status`参数；`slot=refresher`中的`refresherStatus`参数；`slot=chatLoading`中的`loadingMoreStatus`参数。更新版本请特别留意！  
## 2.8.0（2024-10-21）
1.`新增` 全面支持鸿蒙Next。  
2.`修复` 设置了`refresher-complete-delay`后，在下拉刷新期间调用reload导致的无法再次下拉刷新的问题。  
3.`优化` 废弃虚拟列表transformY顶部占位方案，修改为空view占位。解决因使用旧方案导致的vue3中可能出现的虚拟列表闪动问题。提升虚拟列表的兼容性。  
## 2.7.12（2024-09-22）
1.`新增` 虚拟列表+吸顶演示(一般写法)演示&`@virtualPlaceholderTopHeight`，用于监听虚拟列表顶部占位高度改变。  
2.`新增` 聊天记录模式+虚拟列表演示&虚拟列表兼容说明。  
3.`新增` 聊天记录模式中长按cell显示操作pop的示例和说明。  
4.`修复` nvue中底部固定加载更多高度未跟随unit适配的问题。  
5.`修复` 在iOS中可能出现的本地分页空白的问题。  
6.`修复` `to-bottom-loading-more-enabled` 无效的问题。  
7.`优化` 在聊天记录模式中，点击返回顶部按钮调整为点击返回底部。  
8.`优化`  `refresher-enabled`设置为false时调用`reload(true)`也允许显示下拉刷新view。  
## 2.7.11（2024-06-28）
1.`新增` 方法`updateVirtualListRender`，支持手动触发虚拟列表渲染更新。  
2.`新增` 延迟加载列表演示。  
3.`修复` v2.7.8引出的vue3+npm+微信小程序中，`uni.$zp`配置失效的问题。  
4.`修复` 在本地分页+虚拟列表情况下，虚拟列表cell未被正常销毁的问题。  
5.`修复` 打开调试模式下无法获取getApp导致的`cannot read property 'zp_handleQueryCallback' of undefined`的报错。  
6.`修复` 极小概率出现的分页请求较快且快速滚动到底部时未能加载更多的问题。  
## 2.7.10（2024-05-10）
1.`修复` v2.7.8引出的vue3+npm+微信小程序中，uni.$zp配置失效的问题。  
## 2.7.9（2024-05-10）
1.`修复` 在新版HbuilderX+vue3+微信小程序中可能出现的加载第二页数据后返回顶部无法下拉的问题。  
2.`修复` 在vue3+抖音小程序中可能出现的首次加载reload没有触发的问题。  
3.`优化` ts类型中，`ZPagingInstance`泛型不必填，默认为`any`。  
## 2.7.8（2024-05-09）
1.`新增` typescript类型声明文件，感谢小何同学提供。  
2.`新增` 添加极简写法fetch相关配置及示例。  
3.`新增` `fixed-cellHeight`配置，支持在虚拟列表中自定义固定的cell高度。  
4.`新增` `refresher-refreshing-scrollable`配置，支持自定义下拉刷新中是否允许列表滚动。  
5.`修复` 在新版HubilderX+vue3+h5中，`swiper-demo`模式`slot=top`被导航栏遮挡的问题。  
6.`修复` 下拉进入二楼偶现的二楼高度未铺满全屏的问题。  
7.`修复` 虚拟列表中complete若传相同对象会导致key重复的问题。  
8.`修复` 在虚拟列表中调用refresh后缓存高度原始数据未清空的问题。  
9.`修复` 聊天记录模式删除记录时顶部显示loading的问题。  
9.`优化` `scrollIntoViewByIndex`支持在虚拟列表中滚动到指定cell。  
10.`优化` `content-z-index`默认值修改为1。  
## 2.7.7（2024-04-01）
1.`新增` 下拉进入二楼功能及相关配置&demo。  
2.`新增` 虚拟列表写法添加【非内置列表】写法，可良好兼容vue3中的各平台并有较优的性能表现。  
3.`新增` `z-paging-cell`补充`@touchstart`事件。  
4.`修复` 页面滚动模式设置了`auto-full-height`后可能出现的依然有异常空白占位的问题和下拉刷新时列表数据被切割的问题。  
## 2.7.6（2024-02-29）
1.`新增` `max-width`，支持设置`z-paging`的最大宽度，默认`z-paging`宽度铺满窗口。  
2.`新增` `chat-adjust-position-offset`，支持设置使用聊天记录模式中键盘弹出时占位高度偏移距离。    
3.`修复` 由于renderjs中聊天记录模式判断不准确导致的可能出现的从聊天记录页面跳转到其他页面后返回页面无法滚动的问题。  
4.`修复` 聊天记录模式首次加载失败后，发送消息顶部会显示加载失败文字的问题。  
5.`修复` 聊天记录模式nvue可能出现的键盘弹出无法滚动到底部的问题。  
6.`修复` 聊天记录模式+nvue滚动条无法展示的问题&底部会显示加载中的问题。  
7.`修复` 聊天记录模式监听键盘弹出可能出现的无法正常销毁的问题。  
8.`修复` 直接修改dataList的值组件内部的值未更新的问题。  
## 2.7.5（2024-01-23）
1.`新增` props：`chat-loading-more-default-as-loading`，支持设置在聊天记录模式中滑动到顶部状态为默认状态时，以加载中的状态展示。  
2.`新增` slots：`chatNoMore`，支持自定义聊天记录模式没有更多数据view。  
3.`修复` 固定在底部view可能出现默认黄色的问题。  
4.`优化` 聊天记录加载更多样式，与普通模式对齐，支持点击加载更多&点击重试，并支持加载更多相关配置。  
5.`优化` 微调下拉刷新和底部加载更多样式。  
6.`优化` 聊天记录模式自动滚动到底部添加延时以避免可能出现的滚动到底部位置不正确的问题。  
7.`优化` 使用新的判断滚动到顶部算法以解决在安卓设备中可能出现的因滚动到顶部时scrollTop不为0导致的下拉刷新被禁用的问题。  
## 2.7.4（2024-01-14）
1.`新增` props:`auto-adjust-position-when-chat`，支持设置使用聊天记录模式中键盘弹出时是否自动调整slot="bottom"高度。  
2.`新增` props:`auto-to-bottom-when-chat`，支持设置使用聊天记录模式中键盘弹出时是否自动滚动到底部。  
3.`新增` props:`show-chat-loading-when-reload`，支持设置使用聊天记录模式中reload时是否显示chatLoading。  
4.`修复` 在聊天记录模式中`scrollIntoViewById`和`scrollIntoViewByNodeTop`无效的问题。  
5.`优化` 聊天记录模式底部安全区域针对键盘开启/关闭兼容处理。  
6.`优化` 更新内置的空数据图&加载失败图，感谢图鸟UI提供的免费可商用的空数据图和加载失败图！  
## 2.7.3（2024-01-10）
1.`新增` 聊天记录模式支持虚拟列表&添加相关demo。  
2.`新增` nvue中list添加`@scrollend`监听。  
3.`优化` 聊天记录模式+vue第一页并且没有更多时不倒置列表。  
4.`优化` 聊天记录模式+nvue中数据不满屏时默认从顶部开始，不进行列表倒置。  
## 2.7.2（2024-01-09）
1.`修复` `vue3+h5`中报错`uni.onKeyboardHeightChange is not a function`的问题。  
2.`优化` 聊天记录模式细节：表情面板在触摸列表时隐藏&添加隐藏动画。  
## 2.7.1（2024-01-08）
1.`新增` `keyboardHeightChange` event，支持监听键盘高度改变。  
2.`新增` 聊天记录模式新增切换表情面板/键盘demo。  
3.`优化` 键盘弹出占位添加动画效果。  
## 2.7.0（2024-01-07）
2024新年快乐！！祝大家在新的一年里工作顺利，事事顺心！  
1.`新增` 全新的聊天记录模式设计！将vue中的聊天记录模式与nvue中对齐，完全解决了聊天记录模式滚动到顶部加载更多在vue中抖动的问题，同时将聊天记录模式键盘自动弹出自动上推页面交由`z-paging`处理，解决了由此引发的各种问题，尤其是在微信小程序中导航栏被键盘顶出屏幕外的问题。如果您使用了`z-paging`的聊天记录模式，强烈建议更新，写法有一定变更，具体请参见demo。  
2.`新增` `swiper-demo`新增`onShow`时候调用reload演示。  
3.`修复` 修复滚动相关方法在微信小程序中首次滚动动画无效的问题。  
4.`修复` props设置单位，单位为px时报错的问题。  
5.`修复` 在某些情况下`z-paging`加载了但是未渲染时，reload无效的问题。  
6.`修复` 底部loading动画未生效的问题。  
