<template>
  <view class="bluetooth-example">
    <view class="status-section">
      <text>蓝牙状态: {{ isAvailable ? '可用' : '不可用' }}</text>
      <text>连接状态: {{ isConnected ? '已连接' : '未连接' }}</text>
      <text>搜索状态: {{ isDiscovering ? '搜索中' : '未搜索' }}</text>
      <text>加载状态: {{ isLoading ? '加载中' : '空闲' }}</text>
    </view>
    
    <view class="device-section">
      <text>发现的设备数量: {{ devices.length }}</text>
      <view v-for="device in devices" :key="device.deviceId" class="device-item">
        <text>{{ device.name }} ({{ device.RSSI }}dBm)</text>
        <button @click="connectToDevice(device.deviceId)">连接</button>
      </view>
    </view>
    
    <view class="action-section">
      <button @click="startDiscovery">开始搜索</button>
      <button @click="stopDiscovery">停止搜索</button>
      <button @click="pressure_startMeasurement" :disabled="!isConnected">开始测量</button>
    </view>
    
    <view class="connected-device" v-if="connectedDevice">
      <text>当前连接设备: {{ connectedDevice.name }}</text>
    </view>
  </view>
</template>

<script setup lang="ts">
import { useBlueToothData } from '@/common/BlueTooth';

// 使用全局蓝牙数据
const {
  // 状态
  state,
  devices,
  isAvailable,
  isConnected,
  isDiscovering,
  isLoading,
  connectedDevice,
  error,
  
  // 方法
  startDiscovery,
  stopDiscovery,
  connectToDevice,
  pressure_startMeasurement
} = useBlueToothData();

// 监听错误
watch(() => error.value, (newError) => {
  if (newError) {
    uni.showToast({
      title: newError,
      icon: 'error'
    });
  }
});
</script>

<style scoped>
.bluetooth-example {
  padding: 20rpx;
}

.status-section,
.device-section,
.action-section,
.connected-device {
  margin-bottom: 40rpx;
  padding: 20rpx;
  border: 1px solid #eee;
  border-radius: 10rpx;
}

.device-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10rpx 0;
  border-bottom: 1px solid #f0f0f0;
}

button {
  padding: 10rpx 20rpx;
  margin: 10rpx;
  border: none;
  border-radius: 5rpx;
  background-color: #007aff;
  color: white;
}

button:disabled {
  background-color: #ccc;
}
</style>
