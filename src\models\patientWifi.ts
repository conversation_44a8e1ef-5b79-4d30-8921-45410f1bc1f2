export interface PatientsInfo {
    deviceNo: string;       // 设备号，字符串类型
    patientName: string;    // 患者姓名，字符串类型
    patientId: number;
    homeWifiList: Wifi[];   // Wi-Fi 列表，数组类型，数组中的每一项都是 Wifi 类型
    onlineState: string;      // 是否在线
  }

  export interface Wifi {
    wifiId: string;        // Wi-Fi 的 ID，数字类型
    patientId: number;    // 患者 ID，数字类型
    patientName: string; 
    wifiName: string;     // Wi-Fi 名称，字符串类型
    wifiPassword: string; // Wi-Fi 密码，字符串类型
    [key: string]: any;     // 允许多个动态属性，键为字符串类型，值可以是任意类型
  }