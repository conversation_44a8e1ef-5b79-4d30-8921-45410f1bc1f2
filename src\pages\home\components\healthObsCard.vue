<template>
	<view class="grid-container">
		<view v-for="(item, index) in cardList.list" :key="index"
			:class="{'grid-item': true, 'last-item': index === cardList.list.length - 1 && cardList.list.length % 2 !== 0}">
			<view class="flex flex-col bg-[#ffffff] rounded-[16rpx] m-[15rpx] p-[20rpx]" @click="onItemClick(item, index)">
				<view class="flex flex-row">
					<!-- <image style="width: 60rpx;height: 60rpx;" :src="item.icon"></image> -->
					<uni-icons custom-prefix="iconfont" :type="item.icon" size="38" color="#014777"></uni-icons>
					<view class="ml-[20rpx]">
						<view class="text-lg text-[#014777] text-left">{{ item.typeName }}</view>
						<text class="text-sm text-[#333333]">{{ item.measureTime }}</text>
					</view>
				</view>

				<view class="flex flex-row pt-[12rpx] justify-center">
					<text v-if="item.timeFrameName">{{item.timeFrameName ? item.timeFrameName : ''}}</text>
					<view v-if="item.timeFrameName" class="flex-1"></view>
					<view class="flex flex-row">
						<text class="text-2xl">{{item.measureValue}}</text>
						<text class="">{{ item.measureResultFlag }}</text>
					</view>
				</view>
				<view class="flex flex-row justify-between pt-[16rpx]">
					<view><uni-icons type="down" size="16" color="#003B64"></uni-icons></view>
					<view class="flex flex-row justify-between">
						<text class="text-sm">{{ item.measureValueUnit }}</text>
					</view>
				</view>
			</view>
		</view>
	</view> 
</template>

<script setup lang="ts">
	import { reactive, computed, watch } from 'vue';
	import type { IHealthObsResp } from '@/models/healthMeasure'


	const props = withDefaults(
		defineProps<{
			data : IHealthObsResp[];
		}>(),
		{
			data: () => [],
		}
	);

	const cardList = reactive({
		list: props.data,
	});
	watch(
		() => props.data,
		(value) => {
			cardList.list = value;
		}
	);
	const emit = defineEmits(['cardClick', 'cardAdd', 'cardRemove']);
	const onItemClick = (item : IHealthObsResp, clickIndex : number) => {
		emit('cardClick', Object.assign(item, { clickIndex: clickIndex }));
	};
</script>

<style lang="scss" scoped>
	.grid-container {
		display: flex;
		flex-wrap: wrap;
	}

	.grid-item {
		flex: 0 0 50%;
		/* 默认每个元素占50%的宽度 */
		box-sizing: border-box;
		/* 根据需要调整内边距 */
		text-align: center;
	}

	.last-item {
		flex: 0 0 100%;
		/* 最后一个元素占100%的宽度 */
	}
</style>