<template>
	<view class="container">
		<view class="plan">
			<uni-easyinput type="text" v-model="form.phone" placeholder="请输入您的手机号" placeholderStyle="color: #718096;"
				class="input" :inputBorder="false"></uni-easyinput>
			<!-- <view class="flex">
	            <uni-easyinput type="text" v-model="form.code" placeholder="请输入验证码"
	            placeholderStyle="color: #718096;" class="input input-code"
	            :inputBorder="false"></uni-easyinput>
	            <view class="button-code" :style="{ background: countDown > 0 ? '#b6bcbf' : '#00DCA7' }" @click="onClickCode">{{ countDown > 0 ? countDown + '秒' : '获取验证码' }}</view>
	        </view> -->
			<uni-easyinput type="password" v-model="form.password" placeholder="请设置密码（8位数字字母结合）"
				placeholderStyle="color: #718096;" class="input" :inputBorder="false"></uni-easyinput>
			<uni-easyinput type="password" v-model="form.surePassword" placeholder="确认密码" placeholderStyle="color: #718096;"
				class="input" :inputBorder="false"></uni-easyinput>
			<view class="agreed">注册即代表您接受同意<text class="text-blue">《注册协议》</text></view>
		</view>
		<view class="xx-button" @click="onClickSure">注册</view>
		<view class="text-01">已有账号？<text @click="onClickLogin" class="text-login">立即登录</text></view>

		<uni-popup ref="alertDialog" type="dialog">
			<uni-popup-dialog type="success" cancelText="关闭" confirmText="好的" title="通知" content="注册成功！"
				@confirm="dialogClose" @close="dialogClose"></uni-popup-dialog>
		</uni-popup>
	</view>
</template>

<script lang="ts" setup>
	import { ref } from 'vue';
	
	const form = ref<{ username : string; password : string; phone : string; surePassword : string; }>({ username: '', password: '', phone: '', surePassword: '' });
	const onClickSure = () => {

	}
	const onClickLogin = () => {

	}
	const dialogClose = () => {

	}
</script>

<style lang="scss" scoped>
	.container {
		background: white;
		height: 100%;
		text-align: center;
		padding: 120rpx 40rpx 0;

		.plan {
			padding: 40rpx 20rpx 60rpx;

			.input {
				border-bottom: 1px solid #eee;
				font-size: 32rpx;
				padding-bottom: 18rpx;
				margin-bottom: 34rpx;
			}
		}

		.agreed {
			color: #BAC1CC;
			font-size: 28rpx;
			text-align: left;
			padding: 20rpx 0 80rpx;
		}

		.xx-button {
			border-radius: 28rpx;
			height: 108rpx;
			line-height: 108rpx;
			font-size: 40rpx;
			font-weight: bold;
			margin: 0 20rpx;
			position: relative;
			top: -40rpx;
			background: #0D5BAB;
			color: white;
		}

		.text-blue {
			color: #0052A8;
		}

		.text-01 {
			color: #0D5BAB;
		}

		.text-login {
			font-size: 32rpx;
			text-decoration: underline;
		}

		.button-code {
			position: relative;
			bottom: -16rpx;
			margin-left: 8rpx;
			width: 192rpx;
			height: 72rpx;
			line-height: 72rpx;
			color: white;
			border-radius: 8rpx;
			background: #00DCA7;
			font-size: 24rpx;
		}
	}
</style>