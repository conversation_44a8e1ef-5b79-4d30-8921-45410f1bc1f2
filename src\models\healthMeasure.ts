

export interface IHealthObsResp {
	type : string
	id : string | number
	typeName : string
	/* 期望是后端返回，但现在是前端维护字典 */
	icon : string
	require ?: string[]
	measureValue : string | number
	measureValueUnit : string
	pulse : string | number
	level : string | number
	source : string | number
	personId : string | number
	personName : string
	measureTime : string
	hr : string | number
	timeFrame : string | number
	timeFrameName : string
	height : string | number
	weight : string | number
	deviceNo : string
	others : string
	[keys : string] : any
}

interface bpListItem {
	bg : number
	bgLevel : number
	measureTime : string
	others : string
	personId : number
	personName : string
	recordId : number
	source : number
	timeFrame : number
	timeFrameName : string
	bpLevel : number
	dbp : number
	deviceNo : string
	pulse : number
	sbp : number
	[keys : string] : any
}

interface spozListItem {
	bg : number
	bgLevel : number
	measureTime : string
	others : string
	personId : number
	personName : string
	recordId : number
	source : number
	timeFrame : number
	timeFrameName : string
	deviceNo : string
	hr : number
	spoz : number
	spozLevel : number
	[keys : string] : any
}

interface tempListItem {
	measureTime : string
	others : string
	personId : number
	personName : string
	recordId : number
	source : number
	timeFrame : number
	timeFrameName : string
	deviceNo : string
	temp : number
	tempLevel : number
	[keys : string] : any
}

interface bgListItem {
	bg : number
	bgLevel : number
	measureTime : string
	others : string
	personId : number
	personName : string
	recordId : number
	source : number
	timeFrame : number
	timeFrameName : string
	[keys : string] : any
}

interface bmiListItem {
	bmi : number
	bmiLevel : number
	height : number
	measureTime : string
	others : string
	personId : number
	personName : string
	recordId : number
	source : number
	weight : number
	[keys : string] : any
}
export interface PersonSignRecord {
	bpList : bpListItem[]
	spozList : spozListItem[]
	tempList : tempListItem[]
	bgList : bgListItem[]
	bmiList : bmiListItem[]
	[keys : string] : any
}