<template>
	<view class="">

	</view>
</template>

<script setup lang="ts">
	import { onMounted, ref } from "vue";
	import { onLoad, onShow } from "@dcloudio/uni-app";

	onLoad(() => {
		console.log('SPLASH【onLoad】：页面加载完成')
	})

	onShow(() => {
		console.log("SPLASH【onShow】：页面重新可见");

		//检查token情况
		setTimeout(() => {
			goHome();
		}, 1500);
	});

	const goLogin = () => {
		uni.navigateTo({
			url: '/pages/login',
		})
	}

	const goHome = () => {
		uni.navigateTo({
			url: '/page_index/index/index',
		})
	}
</script>

<style scoped lang="scss">

</style>