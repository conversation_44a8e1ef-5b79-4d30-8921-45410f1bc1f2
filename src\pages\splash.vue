<template>
	<view class="">

	</view>
</template>

<script setup lang="ts">
	import { onMounted, ref } from "vue";
	import { onLoad, onShow } from "@dcloudio/uni-app";

	import { storeToRefs } from 'pinia'
	import { useUserInfo } from '@/stores/userInfo'

	onLoad(() => {
		console.log('SPLASH【onLoad】：页面加载完成')
	})

	onShow(() => {
		console.log("SPLASH【onShow】：页面重新可见");

		const storeUserInfo = useUserInfo();
		const { token } = storeToRefs(storeUserInfo);
		console.log(">>>>>>>>>>>>>>>>>>>>>>>>: " + token.value);
		//TODO 检查token情况
		setTimeout(() => {
			if (token.value) {
				goHome();
			} else {
				goLogin();
			}

		}, 1500);
	});

	const goLogin = () => {
		uni.navigateTo({
			url: '/pages/login',
		})
	}

	const goHome = () => {
		uni.reLaunch({
			url: '/pages/home/<USER>',
		})
	}
</script>

<style scoped lang="scss">

</style>