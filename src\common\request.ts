
import environment from '@/common/environments' // 环境，服务配置文件
import mockApiConfig from '@/mock/mock-api-config';
import { useUserInfo } from '@/stores/userInfo'
import { storeToRefs } from 'pinia'
import {
	ref,
	onMounted
} from 'vue';
const VITE_APP_MODE = import.meta.env.VITE_APP_MODE

const CONTENT_TYPE = {
	JSON: 'application/json;charset=UTF-8',
	FORM_URLENCODED: 'application/x-www-form-urlencoded;charset=UTF-8',
	FORM_DATA: 'multipart/form-data;charset=UTF-8',
}

export interface requestType {
	code : number
	data ?: any
	message : string
	status : boolean
}
type ServeType = 'AUTH' | 'PROPERTY' | 'FILE' | 'JOB' | 'BPM'

type HttpMethod = 'POST' | 'OPTIONS' | 'GET' | 'HEAD' | 'PUT' | 'DELETE' | 'TRACE' | 'CONNECT'
type ContentType = 'JSON' | 'FORM_URLENCODED'
const DEFAULT_SERVE : ServeType = 'AUTH'
const DEFAULT_METHOD : HttpMethod = 'GET'
const DEFAULT_CONTENT_TYPE : ContentType = 'JSON'

interface Request {
	serve ?: ServeType
	url : string
	method : HttpMethod
	body ?: any
	hideLoading ?: boolean
	contentType ?: ContentType
	headers ?: any
	timeout ?: number
	custom ?: any
}
const request = async ({
	serve = DEFAULT_SERVE,
	url = '',
	method = DEFAULT_METHOD,
	body = {},
	hideLoading = true, //是否显示请求loading
	contentType = DEFAULT_CONTENT_TYPE,
	headers = {},
	timeout = 30 * 1000,
	custom = {}
} : Request) : Promise<requestType> => {
	if (!hideLoading) {
		uni.showLoading({
			mask: true,
		})
	}

	const requestUrl = environment.filterURL(url)

	if (custom && custom.hasOwnProperty('isMockApi') && custom.isMockApi) {
		console.log("模拟接口，直接callback");

		//卡住500ms，模拟有网络请求的过程
		for (var t = Date.now(); Date.now() - t <= 500;);

		//截取url里面第一个/后面的内容
		let apiPath = 'rest/measure/lastRecord';
		//取data.url
		let mockResult = mockApiConfig.getMockResponse(apiPath, method);
		return Promise.resolve(mockResult);
	} else {
		// console.log("【不是MOCK】开始连接服务器：" + requestUrl);
	}


	const storeUserInfo = useUserInfo();
	// console.log(storeUserInfo);
	const { token } = storeToRefs(storeUserInfo);
	// console.log("token: " + JSON.stringify(token.value));
	//const token = ref<string>('eyJhbGciOiJIUzI1NiJ9.eyJwaG9uZU51bWJlciI6IjEzODA3ODc5NTE0IiwidXNlcklkIjoiMiIsImV4cCI6MTczMzk5NDM1N30.X9xQJQRtf-B0qxHLfYd1DyfkDclfzQK8TDHWzrGKOn0');
	const authHeaders = {
		//Authorization: token.value ? `Bearer ${token.value}` : '',
		//Cookie: token.value ? `access_token=Bearer ${token.value}` : '',
		aptSessionId: token.value,
	}
	try { 
		const res : any = await uni.request({
			url: requestUrl,
			method: method,
			data: body,
			header: Object.assign(authHeaders, headers),
			timeout: 1000 * 10,
			enableCache: true, // 自动处理缓存
		})

		if (res.statusCode !== 200) {
			uni.showToast({
				icon: 'none',
				title: '系统错误！',
			})
			return Promise.reject('系统错误')
		}

		const { data } : any = res;

		//判断是否忽略响应拦截器
		if (custom && custom.ignoreRespInterceptors) {
			//由响应的末端自行处理响应信息
			return Promise.resolve(data)
		}

		if (res.data.success || res.data.code === 421 || res.data.code === 420) {
			return Promise.resolve(data)
		}

		//res.data.success不为true时
		
		if (res.data.code === 601) {
			//需要重新登录清除store
			//userStore().$reset()

			//这里配置登录页页面路径
			uni.reLaunch({
				url: '/pages/login',
			})
			return Promise.reject('需要重新登录')
		}

		uni.showToast({
			icon: 'none',
			title: res.data.msg || '系统错误',
		})
		//异常抛出
		return Promise.reject(res.data.msg || '系统错误')
	} catch (error: any) {
    uni.showToast({
      icon: 'error',
      title: '请求失败！' + error?.errMsg,
    })
		return Promise.reject('请求失败')
	} finally {
		if (!hideLoading) {
			uni.hideLoading()
		}
	}
}
export default request