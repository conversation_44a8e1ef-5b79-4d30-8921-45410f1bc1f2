import environment from '@/common/environments' // 环境，服务配置文件

const BASE_PATH = '/app/version/abpm-web/'
function fetchFreshJSON(url) {
    const freshURL = `${url}${url.includes('?') ? '&' : '?'}_t=${Date.now()}`;

    return new Promise((resolve, reject) => {
        uni.request({
            url: environment.envConfigs.url + freshURL,
            method: 'GET',
            header: {
                'Cache-Control': 'no-cache',
                'Pragma': 'no-cache',
            },
            timeout: 5000,
            dataType: 'text', // 自动解析
            success: (res) => {
                if (res.statusCode === 200) {
                    resolve(res.data);
                } else {
                    reject(new Error(`请求失败，状态码：${res.statusCode}`));
                }
            },
            fail: (err) => {
                reject(err);
            }
        });
    });
}
export function downloadNewVersion(downAppUrl) {
    let view = new plus.nativeObj.View("maskView", {
        backgroundColor: "rgba(0,0,0,.5)",
        left: ((plus.screen.resolutionWidth / 2) - 60) +
            "px",
        bottom: "80px",
        width: "120px",
        height: "34px"
    })

    var dtask = plus.downloader.createDownload(environment.envConfigs.url + BASE_PATH + downAppUrl, {
        filename: '_doc/update/' + new Date().getTime() + '/'
    }, function (d, status) {
        // 下载完成
        if (status == 200) {
            plus.runtime.install(d.filename, {
                force: true
            }, function () {
                uni.showModal({
                    title: '安装完成',
                    content: '已完成更新，点击重启应用',
                    showCancel: false,
                    confirmText: '重启',
                    success: () => {
                        //进行重新启动;
                        plus.runtime.restart();
                    },
                })
            }, (e) => {
                uni.showToast({
                    title: 'install fail:' + JSON
                        .stringify(e),
                    icon: 'none'
                })
                view.hide()
                console.log(JSON.stringify(e))
            });
        } else {
            uni.showToast("下载错误 code: " + status);
        }
    });

    view.drawText('开始下载...', {}, {
        size: '14px',
        color: '#FFFFFF'
    });
    view.show()
    // console.log(dtask);
    dtask.addEventListener("statechanged", (e) => {
        if (e && e.downloadedSize > 0) {
            let jindu = ((e.downloadedSize / e.totalSize) *
                100).toFixed(2)
            view.reset();
            view.drawText('下载:' + jindu + '%', {}, {
                size: '14px',
                color: '#FFFFFF'
            });
        }
    }, false);
    dtask.start();
}

export default function checkAppUpdate(options = {}) {
    // isManualCheck = true 时手动检测
    const { isManualCheck = false } = options;

    if (isManualCheck) {
        uni.showLoading({
            title: '加载中...',
            mask: true
        });
    }

    plus.runtime.getProperty(plus.runtime.appid, (widgetInfo) => {
        fetchFreshJSON( BASE_PATH + 'version.json').then(res => {
            const data = JSON.parse(res);
            console.log(data)
            if (!data) {
                return;
            }

            const versionCode = parseInt(widgetInfo.versionCode);
            const downAppUrl = data.url;

            // 不需要提醒更新
            if (!data.remind) {
                if (isManualCheck) {
                    uni.showToast({ title: '已经是最新的版本！', icon: 'none' });
                }
                return;
            }

            // 版本号比较
            if (versionCode >= data.versionCode) {
                if (isManualCheck) {
                    uni.showToast({ title: '已经是最新的版本！', icon: 'none' });
                }
                return;
            }

            if (!isManualCheck) {
                const not_update_app = uni.getStorageSync('not_update_app')
                // 不提示新这个版本
                if (!data.force && not_update_app && not_update_app.versionCode === data.versionCode) {
                    return
                }
            }

            // 升级提示
            uni.showModal({
                title: data.title,
                content: data.content,
                showCancel: data.force ? false : true,
                confirmText: '更新APP',
                cancelText: '暂不',
                success: res => {
                    if (!res.confirm) {
                        if (!isManualCheck) {
                            uni.setStorageSync('not_update_app', {
                                time: new Date().getTime(),
                                versionCode: data.versionCode,
                                platform: plus.os.name,
                            })
    
                            uni.showToast({ title: '已取消，本次更新将不会提醒', icon: 'none' })
                        }
                        return;
                    }

                    // 开始下载
                    downloadNewVersion(downAppUrl);
                }
            });
        }).finally(() => {
            if (isManualCheck) {
                uni.hideLoading();
            }
        });
    });
}