const env : string | undefined = process.env.NODE_ENV
const SERVE_CTX : any = {
	AUTH: env === 'development' ? '/api' : '/',
}
const configs : any = {
	// 生产环境
	production: {
		url: 'https://need.service.i-need.com.cn:19009'
		//url:'https://need.service.i-need.com.cn:39002'
	},
	// 开发环境
	development: {
        url: 'https://need.service.i-need.com.cn:19009',
		urls: {
            auth: 'https://need.service.i-need.com.cn:19009',
            device: 'https://need.service.i-need.com.cn:19009',
            abpm: 'https://need.service.i-need.com.cn:19009'
        }
	},
}

const envConfigs : {
	url : string
} = configs[env as string]


function filterURL(url: string) {
    // 生产环境
    if (env === 'production') {
        return configs.production.url + url
    }

    const urls = configs.development.urls
    for (const key in urls) {
        if (urls.hasOwnProperty(key)) {
            if (url.indexOf(`/${key}`) === 0) {
                return urls[key] + url
            }
        }
    }

    // 如果没有匹配，返回 null 或者其他适合的值
    return null;

}

export default { envConfigs, SERVE_CTX, filterURL }