<template>
	<view>
		<view class="outer">
			<view class="inner">
				<view class="battery">
					<view class="power" :style="{width:num+'rpx',backgroundColor:colors}"></view>
					<text v-if='isElectric' class="leftText">{{Math.ceil(ElectricQuantity)}}</text>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		name: "ElectricQuantity",
		props: {
			ElectricQuantity: {
				type: Number,
				default: 100
			},
			isElectric: {
				type: Boolean,
				default: false
			}
		},
		data() {
			return {
				colors: this.colorbg(),
				num: this.ElectricQuantity / 1.4
			};
		},
		methods: {
			colorbg() {
				if (this.ElectricQuantity > 20) {
					return '#00baad'
				} else {
					return '#ff9055'
				}

			}
		}
	}
</script>

<style lang="scss" scoped>
	.outer {
		height: 100%;
		width: 100%;
	}

	.leftText {
		font-size: 24rpx;
		position: absolute;
		left: 2rpx;
		top: 0;
		line-height: 28rpx;
		color: #6f6f6f;
	}

	.inner {
		display: table-cell;
		vertical-align: middle;
		text-align: center;
	}

	.battery {
		position: relative;
		display: inline-block;
		height: 28rpx;
		width: 60rpx;

		background: #B4B4B4;
		border: 1rpx solid transparent;
		border-radius: 2rpx;
	}

	.battery:before {
		content: "";
		position: absolute;
		background-color: #FFFFFF;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		border-radius: 5rpx;

	}

	.battery:after {
		content: "";
		position: absolute;
		top: calc(50% - 8rpx);
		right: -8rpx;
		height: 16rpx;
		width: 6rpx;
		border-radius: 0 4rpx 4rpx 0;
		background-color: #B4B4B4;
	}

	.power {
		background-color: white;
		display: block;
		position: absolute;
		top: 3rpx;
		left: 3rpx;
		bottom: 3rpx;
		right: 3rpx;
		border-radius: 5rpx;
		-webkit-transform: scaleX(0.5);
		transform: scaleX(0.5);
		-webkit-transform-origin: 0 0;
		transform-origin: 0 0;
		transition: -webkit-transform 1s cubic-bezier(0, 0, 0.28, 0.95);
		transition: transform 1s cubic-bezier(0, 0, 0.28, 0.95);
		transition: transform 1s cubic-bezier(0, 0, 0.28, 0.95), -webkit-transform 1s cubic-bezier(0, 0, 0.28, 0.95);
	}
</style>
