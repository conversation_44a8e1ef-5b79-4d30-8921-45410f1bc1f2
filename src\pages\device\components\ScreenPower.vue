<template>
    <view class="container flex-col xx-flex-center">
        <view class="g-progress-wrap" :style="{ '--rotate-deg': rotate + 'deg' }">
            <view class="g-bg"></view>
            <view class="g-progress"></view>
            <view class="g-circle">
                <span class="g-circle-before"><i/></span>
                <span class="g-circle-after"><i/></span>
            </view>
            <view class="g-text">
                <text class="i-num">{{ capacityPercent }}</text>
                <text class="i-text">%</text>
                <view class="i-tip">充电中</view>
            </view>
        </view>
    </view>
</template>
<script>
export default {
    props: {
        dataMap: Object
    },
    data() {
        return {
            rotate: 0,
            timer: null
        };
    },
    computed: {
        capacityPercent() {
            return this.dataMap.capacityPercent || 0
        }
    },
    watch: {
        capacityPercent: {
            handler() {
                this.setLoop()
            },
            immediate: true
        }
    },
    methods: {
        setRotate(progress) {
            const rotate = progress / 100 * 360
            this.rotate = rotate
        },
        setLoop() {

            let progress = this.capacityPercent
            this.setRotate(progress)

            clearInterval(this.timer)

            if (progress >= 100) {
                console.log('stop')
                return
            }
            this.timer = setInterval(() => {
                progress += 0.1
                this.setRotate(progress)
                if (progress >= 100) {
                    this.setLoop()
                }
            }, 20);
        }
    },
    beforeUnmount() {
        clearInterval(this.timer)
    },
};
</script>
<style lang="scss" scoped>
.container {
    color: white;
    height: 100%;
    border-radius: 40rpx;
    overflow: hidden;
}
.i-last-minute {
    padding-top: 24rpx;
    color: white;
}

$progressSize: 292rpx; // 进度条的尺寸(width, height)
$progressGap: 28rpx; // 圆环的宽度
$activeStartColor: #34DEED;
$activeEndColor: #34DEED;
$notActiveColor: #33deee2f;

.g-progress-wrap {
    display: inline-block;
    position: relative;

    --rotate-deg: 0deg;
    .g-bg {
        width: 292rpx;
        height: 292rpx;
        border-radius: 50%;
        box-shadow: 10rpx 10rpx 60rpx 20rpx #04B0BF inset;
        position: absolute;
    }
    .g-progress {
        width: $progressSize;
        height: $progressSize;
        border-radius: 50%;
        background: conic-gradient($activeStartColor 0,
                $activeEndColor var(--rotate-deg, 0deg),
                $notActiveColor var(--rotate-deg, 0deg),
                $notActiveColor 360deg);
        // box-shadow:inset 0px 4px 8rpx  #C7FAFF;

        $maskSize: 59px;
        mask: radial-gradient(transparent,
                transparent 118rpx,
                #000 138rpx,
                #000 100%
        );
        -webkit-mask: radial-gradient(transparent,
                transparent 118rpx,
                #000 118rpx,
                #000 100%);
        }

    .g-circle {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        transform: rotate(-90deg);
        overflow: hidden;

        &>span {
            position: absolute;
            top: 132rpx;
            left: 146rpx;
            width: 50%;
            transform-origin: left;

            &>i {
                width: $progressGap;
                height: $progressGap;
                float: right;
                border-radius: 50%;
                background: $activeStartColor;
                z-index: 1;
            }
        }

        & .g-circle-after {
            transform: rotate(var(--rotate-deg, 0deg));

            &>i {
                background: $activeEndColor;
            }
        }
    }

    .g-text {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        font-size: 32rpx;
        color: white;
        text-align: center;
        .i-num {
            font-size: 80rpx;
        }
        .i-text {
            padding-left: 6rpx;
        }
        .i-tip {
            font-size: 28rpx;
            margin-top: -10rpx;
        }
    }
}
.xx-flex-center {
    display: flex;
    justify-content: center;
    align-items: center;
}
</style>
